#!/bin/bash

# 🏠 Workspace 完整备份推送系统
# 功能：将混合项目作为完整备份推送到私有库，不影响子项目独立性
# 版本：v2.0
# 作者：AI Assistant
# 特性：大文件支持、历史清理、简单易用

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 子项目配置
declare -A SUBPROJECTS=(
    ["gpt-load"]="https://github.com/CoolKingW/gpt-load.git"
    ["love"]="https://github.com/CoolKingW/love.git"
    ["new-api"]="https://github.com/CoolKingW/new-api.git"
)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待用户按键
wait_for_key() {
    echo
    echo -e "${CYAN}按 Enter 键继续...${NC}"
    read -r
}

# 确认操作 - 默认为 Y
confirm_action() {
    local message="$1"
    local default="${2:-Y}"
    local response

    if [ "$default" = "Y" ]; then
        echo -n -e "${YELLOW}$message [Y/n]: ${NC}"
    else
        echo -n -e "${YELLOW}$message [y/N]: ${NC}"
    fi

    read -r response

    # 如果用户直接按回车，使用默认值
    if [ -z "$response" ]; then
        response="$default"
    fi

    case "$response" in
        [Yy]|[Yy][Ee][Ss])
            return 0
            ;;
        [Nn]|[Nn][Oo])
            return 1
            ;;
        *)
            # 无效输入，使用默认值
            [ "$default" = "Y" ] && return 0 || return 1
            ;;
    esac
}

# 检查是否为 Git 仓库
check_git_repo() {
    local dir="${1:-$PWD}"
    [ -d "$dir/.git" ] && git -C "$dir" rev-parse --git-dir >/dev/null 2>&1
}

# 获取文件大小（字节）
get_file_size() {
    local file="$1"
    if [ -f "$file" ]; then
        stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0"
    else
        echo "0"
    fi
}

# 检查是否为大文件（超过100MB）
is_large_file() {
    local file="$1"
    local max_size=$((100 * 1024 * 1024))  # 100MB
    local file_size=$(get_file_size "$file")
    [ "$file_size" -gt "$max_size" ]
}

# 分析项目状态
analyze_project_status() {
    echo -e "${CYAN}=== 📊 分析项目状态 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 检查主项目状态
    echo "========================================"
    echo "主项目状态"
    echo "========================================"

    if ! check_git_repo; then
        log_error "当前目录不是 Git 仓库"
        return 1
    fi

    local current_branch=$(git branch --show-current 2>/dev/null || echo "未知")
    local current_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "无提交")
    echo "当前分支: $current_branch"
    echo "当前提交: $current_commit"

    # 检查远程仓库
    echo "远程仓库:"
    git remote -v 2>/dev/null || echo "  无远程仓库"
    echo

    # 检查子项目状态
    echo "========================================"
    echo "子项目状态"
    echo "========================================"

    for project in "${!SUBPROJECTS[@]}"; do
        echo "--- $project ---"

        if [ ! -d "$project" ]; then
            log_error "子项目目录不存在: $project"
            continue
        fi

        if [ -d "$project/.git" ]; then
            log_success "子项目有独立Git仓库: $project"
            local remote_url=$(git -C "$project" remote get-url origin 2>/dev/null || echo "无远程仓库")
            echo "  远程仓库: $remote_url"
        else
            log_warning "子项目无Git仓库: $project"
        fi
        echo
    done

    # 检查大文件
    echo "========================================"
    echo "大文件检查 (>100MB)"
    echo "========================================"

    local large_files_found=false
    find . -type f -not -path './.git/*' -not -path './*/node_modules/*' | while read -r file; do
        if is_large_file "$file"; then
            local size_mb=$(( $(get_file_size "$file") / 1024 / 1024 ))
            log_warning "发现大文件: $file (${size_mb}MB)"
            large_files_found=true
        fi
    done

    if [ "$large_files_found" = false ]; then
        log_success "未发现大文件"
    fi

    echo
    log_success "项目状态分析完成"
    return 0
}

# 清理历史跟踪记录
clean_history_tracking() {
    echo -e "${CYAN}=== 🧹 清理历史跟踪记录 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    echo "此操作将："
    echo "1. 从Git历史中移除所有子项目文件的跟踪记录"
    echo "2. 保留子项目的当前文件内容"
    echo "3. 创建全新的干净历史"
    echo "4. 不影响子项目的独立Git仓库"
    echo

    if ! confirm_action "确定要清理历史跟踪记录吗？"; then
        log_info "操作已取消"
        return 1
    fi

    # 备份当前状态
    local backup_branch="backup-before-cleanup-$(date +%Y%m%d_%H%M%S)"
    git branch "$backup_branch" 2>/dev/null || true
    log_success "创建备份分支: $backup_branch"

    # 移除所有子项目文件的跟踪
    log_info "移除子项目文件跟踪..."
    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -1 | grep -q .; then
            log_info "移除 $project/ 的跟踪记录"
            git rm -r --cached "$project/" 2>/dev/null || true
        fi
    done

    # 提交清理更改
    if ! git diff --cached --quiet; then
        git commit -m "Clean: Remove subproject tracking from history

This commit removes all tracking of subproject files from the main
repository history. Subprojects will be managed as independent
repositories while maintaining their content for backup purposes.

Cleaned projects: $(echo "${!SUBPROJECTS[@]}" | tr ' ' ', ')
Cleanup date: $(date -Iseconds)"

        log_success "历史清理提交完成"
    else
        log_info "没有需要清理的跟踪记录"
    fi

    return 0
}

# 建立备份基线
establish_backup_baseline() {
    echo -e "${CYAN}=== 📋 建立备份基线 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 添加主项目文件
    log_info "添加主项目文件..."
    git add README.md git.sh guide.md 2>/dev/null || true
    git add shell/ shared/ 2>/dev/null || true

    # 添加子项目内容（排除.git目录）
    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            log_info "添加子项目内容: $project"

            # 使用find排除.git目录
            find "$project" -type f -not -path "*/.git/*" -print0 | while IFS= read -r -d '' file; do
                git add "$file" 2>/dev/null || true
            done
        fi
    done

    # 检查添加的文件
    local added_files=$(git diff --cached --name-only | wc -l)
    log_info "准备提交 $added_files 个文件"

    if [ "$added_files" -eq 0 ]; then
        log_warning "没有文件需要提交"
        return 0
    fi

    # 提交基线
    local baseline_message="Establish backup baseline

This commit establishes a clean backup baseline containing:
- Main project files and configuration
- Current state of all subprojects (content only)
- Proper .gitignore configuration

Subprojects included: $(echo "${!SUBPROJECTS[@]}" | tr ' ' ', ')
Baseline date: $(date -Iseconds)
Total files: $added_files

Note: Subproject .git directories are excluded to maintain
their independence while preserving complete backup capability."

    if git commit -m "$baseline_message"; then
        log_success "备份基线建立完成"
        return 0
    else
        log_error "备份基线建立失败"
        return 1
    fi
}

# 执行完整备份
perform_full_backup() {
    echo -e "${CYAN}=== 💾 执行完整备份 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 分析当前状态
    analyze_project_status

    echo
    if ! confirm_action "开始执行完整备份？"; then
        log_info "备份已取消"
        return 1
    fi

    # 检查是否有旧的跟踪记录
    local has_old_tracking=false
    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -1 | grep -q .; then
            has_old_tracking=true
            break
        fi
    done

    if [ "$has_old_tracking" = true ]; then
        echo
        log_warning "检测到旧的跟踪记录"
        if confirm_action "是否清理历史跟踪记录？"; then
            clean_history_tracking
        fi
    fi

    # 建立备份基线
    echo
    log_info "建立备份基线..."
    establish_backup_baseline

    # 推送到远程
    echo
    log_info "推送备份到远程仓库..."
    if git push origin $(git branch --show-current) 2>&1; then
        log_success "完整备份完成！"
        return 0
    else
        log_error "备份推送失败"
        return 1
    fi
}

# 增量备份更新
perform_incremental_backup() {
    echo -e "${CYAN}=== 🔄 执行增量备份 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    # 检查是否有更改
    if git diff --quiet && git diff --cached --quiet; then
        # 检查未跟踪的文件
        local untracked_files=$(git ls-files --others --exclude-standard | wc -l)
        if [ "$untracked_files" -eq 0 ]; then
            log_info "没有检测到更改，跳过备份"
            return 0
        fi
    fi

    log_info "检测到项目更改，准备增量备份..."

    # 添加所有更改（排除.git目录）
    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            find "$project" -type f -not -path "*/.git/*" -print0 | while IFS= read -r -d '' file; do
                git add "$file" 2>/dev/null || true
            done
        fi
    done

    # 添加主项目文件
    git add README.md git.sh guide.md shell/ shared/ 2>/dev/null || true

    # 检查是否有更改需要提交
    if git diff --cached --quiet; then
        log_info "没有需要提交的更改"
        return 0
    fi

    # 生成提交消息
    local added_files=$(git diff --cached --name-only | wc -l)
    local commit_msg="Incremental backup update

Updated files: $added_files
Update time: $(date -Iseconds)

This is an automated incremental backup containing
the latest changes to the workspace and all subprojects."

    # 提交更改
    if git commit -m "$commit_msg"; then
        log_success "增量更改已提交"

        # 推送更改
        if git push origin $(git branch --show-current) 2>&1; then
            log_success "增量备份完成"
            return 0
        else
            log_error "增量备份推送失败"
            return 1
        fi
    else
        log_error "增量备份提交失败"
        return 1
    fi
}

# 验证备份完整性
verify_backup_integrity() {
    echo -e "${CYAN}=== ✅ 验证备份完整性 ===${NC}"
    echo

    cd "$WORKSPACE_DIR"

    local errors=0

    # 检查Git仓库完整性
    log_info "检查Git仓库完整性..."
    if git fsck --full 2>/dev/null; then
        log_success "Git仓库完整性检查通过"
    else
        log_error "Git仓库完整性检查失败"
        errors=$((errors + 1))
    fi

    # 检查远程同步状态
    log_info "检查远程同步状态..."
    local local_commit=$(git rev-parse HEAD)
    local remote_commit=$(git rev-parse origin/$(git branch --show-current) 2>/dev/null || echo "unknown")

    if [ "$local_commit" = "$remote_commit" ]; then
        log_success "本地与远程同步"
    else
        log_warning "本地与远程不同步"
        log_info "本地: $local_commit"
        log_info "远程: $remote_commit"
    fi

    # 检查子项目完整性
    log_info "检查子项目完整性..."
    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            local project_files=$(find "$project" -type f -not -path "*/.git/*" | wc -l)
            if [ "$project_files" -gt 0 ]; then
                log_success "$project: $project_files 个文件"
            else
                log_error "$project: 没有文件"
                errors=$((errors + 1))
            fi
        else
            log_error "$project: 目录不存在"
            errors=$((errors + 1))
        fi
    done

    # 总结
    echo
    if [ $errors -eq 0 ]; then
        log_success "备份完整性验证通过"
        return 0
    else
        log_error "发现 $errors 个完整性问题"
        return 1
    fi
}

# 显示使用指南
show_usage_guide() {
    echo -e "${CYAN}=== 📚 完整备份推送系统使用指南 ===${NC}"

    echo
    echo "========================================"
    echo "🎯 系统功能说明"
    echo "========================================"
    echo "本系统将混合项目作为完整备份推送到私有库，具有以下特点："
    echo "• 🔒 保持子项目完全独立：不影响子项目的Git仓库"
    echo "• 💾 完整内容备份：包含所有子项目的完整源代码"
    echo "• 🚀 大文件支持：自动处理大文件上传"
    echo "• 🧹 历史清理：清理旧的跟踪记录，建立干净基线"
    echo "• 📦 智能忽略：自动排除不必要的文件和目录"
    echo

    echo "========================================"
    echo "🚀 常用操作流程"
    echo "========================================"
    echo "1. 首次使用："
    echo "   选择 1 - 分析项目状态"
    echo "   选择 2 - 执行完整备份"
    echo
    echo "2. 日常更新："
    echo "   选择 3 - 增量备份更新"
    echo
    echo "3. 验证备份："
    echo "   选择 4 - 验证备份完整性"
    echo
    echo "4. 系统维护："
    echo "   选择 5 - 清理历史记录"
    echo

    echo "========================================"
    echo "⚠️ 重要说明"
    echo "========================================"
    echo "• 子项目的.git目录会被自动忽略，保持独立性"
    echo "• 大文件会被检测并提示处理方案"
    echo "• 系统会自动清理临时文件和日志"
    echo "• 备份不会影响子项目的正常开发流程"
    echo "• 默认按Enter键表示确认(Y)"
    echo

    wait_for_key
}