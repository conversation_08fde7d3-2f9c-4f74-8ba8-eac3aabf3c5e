#!/bin/bash

# 🏠 Workspace Git Submodules 转换管理器
# 功能：将混合项目转换为标准的 Git Submodules 结构
# 版本：v1.0
# 作者：AI Assistant
# 基于：love/shell/git.sh 的架构设计

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORKSPACE_DIR="$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 子项目配置
declare -A SUBPROJECTS=(
    ["gpt-load"]="https://github.com/CoolKingW/gpt-load.git"
    ["love"]="https://github.com/CoolKingW/love.git"
    ["new-api"]="https://github.com/CoolKingW/new-api.git"
)

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待用户按键
wait_for_key() {
    echo
    echo -e "${CYAN}按任意键继续...${NC}"
    read -n 1 -s
}

# 确认操作
confirm_action() {
    local message="$1"
    local default="${2:-N}"
    local response

    while true; do
        if [ "$default" = "Y" ]; then
            echo -n -e "${YELLOW}$message [Y/n]: ${NC}"
        else
            echo -n -e "${YELLOW}$message [y/N]: ${NC}"
        fi

        read -r response

        # 如果用户直接按回车，使用默认值
        if [ -z "$response" ]; then
            response="$default"
        fi

        case "$response" in
            [Yy]|[Yy][Ee][Ss])
                return 0
                ;;
            [Nn]|[Nn][Oo])
                return 1
                ;;
            *)
                echo -e "${RED}请输入 y/yes 或 n/no${NC}"
                ;;
        esac
    done
}

# 检查是否为 Git 仓库
validate_git_repo() {
    local dir="${1:-$PWD}"
    [ -d "$dir/.git" ] && git -C "$dir" rev-parse --git-dir >/dev/null 2>&1
}

# 强制清理 Git 索引中的子项目跟踪
force_clean_git_index() {
    local project="$1"

    log_info "强制清理 $project 的 Git 索引..."

    # 多种方式清理索引
    git rm -rf --cached "$project" 2>/dev/null || true
    git rm -rf --cached "$project/" 2>/dev/null || true
    git reset HEAD "$project" 2>/dev/null || true
    git reset HEAD "$project/" 2>/dev/null || true

    # 如果仍然存在，使用更激进的方法
    if git ls-files | grep -q "^$project"; then
        log_warning "使用激进方法清理 $project..."

        # 临时提交当前状态
        local temp_commit=""
        if [ -n "$(git status --porcelain)" ]; then
            git add -A
            temp_commit=$(git commit -m "Temporary commit before submodule conversion" --quiet && git rev-parse HEAD)
        fi

        # 使用 filter-branch 移除路径（仅针对最近的提交）
        git filter-branch --index-filter "git rm -rf --cached --ignore-unmatch $project $project/" HEAD~1..HEAD 2>/dev/null || true

        # 清理 filter-branch 的备份
        git update-ref -d refs/original/refs/heads/$(git branch --show-current) 2>/dev/null || true

        # 强制垃圾回收
        git gc --prune=now 2>/dev/null || true
    fi

    # 最终验证
    if git ls-files | grep -q "^$project"; then
        log_error "无法完全清理 $project 的索引跟踪"
        return 1
    else
        log_success "$project 的索引跟踪已完全清理"
        return 0
    fi
}

# 检查子项目状态
check_subproject_status() {
    local project="$1"
    local project_dir="$WORKSPACE_DIR/$project"

    echo -e "${CYAN}检查子项目: $project${NC}"

    if [ ! -d "$project_dir" ]; then
        log_error "子项目目录不存在: $project_dir"
        return 1
    fi

    if ! validate_git_repo "$project_dir"; then
        log_error "子项目不是有效的 Git 仓库: $project"
        return 1
    fi

    # 检查远程仓库
    local remote_url=$(git -C "$project_dir" remote get-url origin 2>/dev/null)
    if [ -z "$remote_url" ]; then
        log_error "子项目没有配置远程仓库: $project"
        return 1
    fi

    # 检查是否有未提交的更改
    if [ -n "$(git -C "$project_dir" status --porcelain)" ]; then
        log_warning "子项目有未提交的更改: $project"
        git -C "$project_dir" status --short
        return 2
    fi

    log_success "子项目状态正常: $project (远程: $remote_url)"
    return 0
}

# 分析当前项目结构
analyze_current_structure() {
    echo -e "${CYAN}=== 📊 分析当前项目结构 ===${NC}"

    cd "$WORKSPACE_DIR"

    # 检查主项目状态
    echo "========================================"
    echo "主项目状态分析"
    echo "========================================"

    if ! validate_git_repo; then
        log_error "当前目录不是 Git 仓库"
        return 1
    fi

    local current_branch=$(git branch --show-current 2>/dev/null || echo "未知")
    local current_commit=$(git rev-parse --short HEAD 2>/dev/null || echo "无提交")
    echo "当前分支: $current_branch"
    echo "当前提交: $current_commit"

    # 检查远程仓库
    echo "远程仓库:"
    git remote -v 2>/dev/null || echo "  无远程仓库"
    echo

    # 检查主项目是否跟踪子项目文件
    echo "主项目跟踪的子项目文件:"
    local tracking_subproject_files=false
    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -5 | grep -q .; then
            echo "  ✗ 正在跟踪 $project/ 目录下的文件"
            tracking_subproject_files=true
        fi
    done

    if [ "$tracking_subproject_files" = false ]; then
        echo "  ✓ 主项目未跟踪子项目文件"
    fi
    echo

    # 检查是否已经有 submodules
    if [ -f ".gitmodules" ]; then
        echo "现有的 Submodules:"
        cat .gitmodules
    else
        echo "当前没有配置 Submodules"
    fi
    echo

    # 检查各子项目状态
    echo "========================================"
    echo "子项目状态分析"
    echo "========================================"

    local all_projects_ok=true
    for project in "${!SUBPROJECTS[@]}"; do
        if ! check_subproject_status "$project"; then
            all_projects_ok=false
        fi
        echo
    done

    echo "========================================"
    echo "结构分析总结"
    echo "========================================"

    if [ "$tracking_subproject_files" = true ]; then
        log_warning "检测到问题：主项目正在跟踪子项目文件"
        echo "  这会导致版本控制冲突，建议转换为 Git Submodules"
    else
        log_success "主项目结构正常"
    fi

    if [ "$all_projects_ok" = true ]; then
        log_success "所有子项目状态正常"
        echo "  可以安全地进行 Submodules 转换"
    else
        log_error "部分子项目存在问题"
        echo "  请先解决子项目问题再进行转换"
        return 1
    fi

    return 0
}

# 预处理：修复索引问题
fix_git_index_issues() {
    echo -e "${CYAN}=== 🔧 修复 Git 索引问题 ===${NC}"

    cd "$WORKSPACE_DIR"

    log_info "检查并修复 Git 索引中的子项目跟踪问题..."

    local issues_found=false

    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files | grep -q "^$project"; then
            log_warning "发现问题：$project 在 Git 索引中"
            issues_found=true

            if confirm_action "是否清理 $project 的索引跟踪？" "Y"; then
                if force_clean_git_index "$project"; then
                    log_success "$project 索引问题已修复"
                else
                    log_error "$project 索引问题修复失败"
                    return 1
                fi
            else
                log_error "用户取消了 $project 的修复"
                return 1
            fi
        else
            log_success "$project 索引状态正常"
        fi
    done

    if [ "$issues_found" = true ]; then
        log_info "提交索引清理更改..."
        if [ -n "$(git status --porcelain)" ]; then
            git add -A
            git commit -m "Clean up subproject tracking from main repository index

This commit removes direct file tracking of subprojects to prepare
for Git Submodules conversion. Each subproject will maintain its
own independent Git repository while being referenced as a submodule."
            log_success "索引清理更改已提交"
        fi
    else
        log_success "所有子项目的索引状态都正常"
    fi

    return 0
}

# 执行 Submodules 转换
execute_submodules_conversion() {
    echo -e "${CYAN}=== 🔄 执行 Git Submodules 转换 ===${NC}"

    cd "$WORKSPACE_DIR"

    # 最终确认
    echo "即将执行以下操作："
    echo "1. 从主项目 Git 索引中移除子项目文件跟踪"
    echo "2. 将子项目转换为 Git Submodules"
    echo "3. 更新 .gitmodules 配置文件"
    echo "4. 提交转换更改"
    echo

    if ! confirm_action "确定要继续执行转换吗？" "N"; then
        log_info "转换已取消"
        return 1
    fi

    # 步骤1: 移除主项目对子项目文件的跟踪
    echo
    log_info "步骤 1/4: 移除主项目对子项目文件的跟踪..."

    for project in "${!SUBPROJECTS[@]}"; do
        log_info "处理项目: $project"

        # 检查是否在索引中
        if git ls-files "$project/" | head -1 | grep -q .; then
            log_info "移除对 $project/ 文件的跟踪..."
            git rm -r --cached "$project/" 2>/dev/null || true
        fi

        # 检查是否作为目录在索引中
        if git ls-files | grep -q "^$project$"; then
            log_info "移除对 $project 目录的跟踪..."
            git rm --cached "$project" 2>/dev/null || true
        fi

        # 使用强制清理函数
        if ! force_clean_git_index "$project"; then
            log_error "无法清理 $project 的索引，转换失败"
            return 1
        fi
    done

    # 额外的索引清理步骤
    log_info "执行额外的索引清理..."
    git add -A  # 暂存所有更改

    # 再次确认清理结果
    echo "当前索引状态:"
    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files | grep -q "^$project"; then
            log_error "$project 仍在索引中！"
            return 1
        else
            log_success "$project 已从索引中清除"
        fi
    done

    # 步骤2: 临时移动子项目目录
    echo
    log_info "步骤 2/4: 临时移动子项目目录..."

    local temp_dir="$WORKSPACE_DIR/.temp_submodules_$(date +%s)"
    mkdir -p "$temp_dir"

    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            log_info "临时移动 $project 到 $temp_dir/"
            mv "$project" "$temp_dir/"
            log_success "$project 已临时移动"
        fi
    done

    # 步骤3: 添加 Git Submodules
    echo
    log_info "步骤 3/4: 添加 Git Submodules..."

    for project in "${!SUBPROJECTS[@]}"; do
        local repo_url="${SUBPROJECTS[$project]}"
        log_info "准备添加 submodule: $project ($repo_url)"

        # 最终检查：确保目录不在索引中
        if git ls-files | grep -q "^$project"; then
            log_error "$project 仍在 Git 索引中，无法添加为 submodule"
            log_info "尝试最终清理..."
            git rm -rf --cached "$project" 2>/dev/null || true
            git reset HEAD "$project" 2>/dev/null || true

            # 再次检查
            if git ls-files | grep -q "^$project"; then
                log_error "无法清理 $project 的索引状态，请手动处理"
                return 1
            fi
        fi

        log_info "添加 submodule: $project"

        # 尝试添加 submodule
        if git submodule add "$repo_url" "$project" 2>&1; then
            log_success "Submodule $project 添加成功"

            # 恢复子项目的本地更改（如果有的话）
            if [ -d "$temp_dir/$project" ]; then
                log_info "恢复 $project 的本地状态..."

                # 进入 submodule 目录
                cd "$project"

                # 检查是否有本地更改需要恢复
                local temp_project_dir="$temp_dir/$project"
                if [ -d "$temp_project_dir" ]; then
                    # 比较文件差异（排除 .git 目录）
                    if ! diff -r --exclude='.git' . "$temp_project_dir" >/dev/null 2>&1; then
                        log_info "检测到本地更改，正在恢复..."

                        # 复制非 .git 文件
                        rsync -av --exclude='.git' "$temp_project_dir/" ./

                        log_success "$project 的本地状态已恢复"
                    else
                        log_info "$project 无需恢复本地更改"
                    fi
                fi

                cd "$WORKSPACE_DIR"
            fi
        else
            local error_msg="添加 submodule $project 失败"
            log_error "$error_msg"

            # 显示详细错误信息
            echo "可能的原因："
            echo "1. 远程仓库不存在或无法访问"
            echo "2. 目录仍在 Git 索引中"
            echo "3. 网络连接问题"
            echo "4. 权限问题"

            # 恢复原始目录
            if [ -d "$temp_dir/$project" ]; then
                if [ -d "$project" ]; then
                    rm -rf "$project"
                fi
                mv "$temp_dir/$project" "$project"
                log_info "已恢复原始 $project 目录"
            fi

            return 1
        fi
    done

    # 清理临时目录
    rm -rf "$temp_dir"

    # 步骤4: 提交更改
    echo
    log_info "步骤 4/4: 提交转换更改..."

    # 添加 .gitmodules 文件
    git add .gitmodules

    # 添加 submodule 引用
    for project in "${!SUBPROJECTS[@]}"; do
        git add "$project"
    done

    # 提交更改
    local commit_msg="Convert to Git Submodules structure

- Remove direct tracking of subproject files
- Add submodules: $(echo "${!SUBPROJECTS[@]}" | tr ' ' ', ')
- Configure .gitmodules for proper submodule management

This conversion enables:
- Independent subproject development
- Unified project deployment
- Version consistency across environments"

    if git commit -m "$commit_msg"; then
        log_success "转换更改已提交"

        # 显示转换结果
        echo
        echo "========================================"
        echo "转换完成！"
        echo "========================================"
        echo "✓ 主项目不再直接跟踪子项目文件"
        echo "✓ 子项目已转换为 Git Submodules"
        echo "✓ .gitmodules 配置文件已创建"
        echo "✓ 转换更改已提交到主项目"
        echo
        echo "下一步操作："
        echo "1. 推送主项目更改: git push origin $(git branch --show-current)"
        echo "2. 在新环境中使用: git clone --recursive <repo-url>"
        echo "3. 更新 submodules: git submodule update --remote"

    else
        log_error "提交转换更改失败"
        return 1
    fi

    return 0
}

# 验证转换结果
verify_conversion() {
    echo -e "${CYAN}=== ✅ 验证转换结果 ===${NC}"

    cd "$WORKSPACE_DIR"

    echo "========================================"
    echo "转换结果验证"
    echo "========================================"

    # 检查 .gitmodules 文件
    if [ -f ".gitmodules" ]; then
        log_success ".gitmodules 文件存在"
        echo "内容:"
        cat .gitmodules | sed 's/^/  /'
        echo
    else
        log_error ".gitmodules 文件不存在"
        return 1
    fi

    # 检查 submodules 状态
    echo "Submodules 状态:"
    if git submodule status; then
        log_success "所有 submodules 状态正常"
    else
        log_error "Submodules 状态异常"
        return 1
    fi
    echo

    # 检查主项目是否还在跟踪子项目文件
    echo "主项目文件跟踪检查:"
    local still_tracking=false
    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -1 | grep -q .; then
            log_error "主项目仍在跟踪 $project/ 文件"
            still_tracking=true
        else
            log_success "主项目不再跟踪 $project/ 文件"
        fi
    done

    if [ "$still_tracking" = true ]; then
        log_error "转换不完整，主项目仍在跟踪部分子项目文件"
        return 1
    fi
    echo

    # 检查子项目独立性
    echo "子项目独立性检查:"
    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ] && validate_git_repo "$project"; then
            local remote_url=$(git -C "$project" remote get-url origin 2>/dev/null)
            log_success "$project: 独立 Git 仓库 ($remote_url)"
        else
            log_error "$project: 不是有效的独立 Git 仓库"
            return 1
        fi
    done

    echo
    log_success "🎉 Git Submodules 转换验证通过！"
    echo
    echo "转换成功完成，你现在可以："
    echo "• 推送主项目到私有库"
    echo "• 子项目保持完全独立"
    echo "• 使用统一的部署和管理"

    return 0
}

# Submodules 日常管理
manage_submodules() {
    echo -e "${CYAN}=== 🔧 Submodules 日常管理 ===${NC}"

    cd "$WORKSPACE_DIR"

    if [ ! -f ".gitmodules" ]; then
        log_error "当前项目没有配置 Submodules"
        echo "请先执行转换操作"
        return 1
    fi

    while true; do
        echo
        echo "========================================"
        echo "Submodules 管理选项"
        echo "========================================"
        echo "1) 📊 查看 submodules 状态"
        echo "2) 🔄 更新所有 submodules"
        echo "3) 🔄 更新指定 submodule"
        echo "4) 📥 初始化 submodules（新克隆后）"
        echo "5) 🔧 修复 submodule 问题"
        echo "6) 📋 显示 submodules 信息"
        echo "0) 返回主菜单"
        echo
        read -p "请选择操作 [0-6]: " choice

        case $choice in
            1)
                echo
                log_info "Submodules 状态:"
                git submodule status
                echo
                log_info "详细信息:"
                git submodule foreach 'echo "=== $name ==="; git status --short; git log --oneline -3'
                wait_for_key
                ;;
            2)
                echo
                log_info "更新所有 submodules..."
                if git submodule update --remote; then
                    log_success "所有 submodules 更新完成"

                    # 检查是否有更新
                    if [ -n "$(git status --porcelain)" ]; then
                        echo
                        log_info "检测到 submodule 更新，是否提交？"
                        git status --short
                        if confirm_action "提交 submodule 更新？" "Y"; then
                            git add .
                            git commit -m "Update submodules to latest versions"
                            log_success "Submodule 更新已提交"
                        fi
                    fi
                else
                    log_error "更新 submodules 失败"
                fi
                wait_for_key
                ;;
            3)
                echo
                echo "可用的 submodules:"
                git submodule | sed 's/^/  /'
                echo
                read -p "输入要更新的 submodule 名称: " submodule_name
                if [ -n "$submodule_name" ] && [ -d "$submodule_name" ]; then
                    log_info "更新 submodule: $submodule_name"
                    if git submodule update --remote "$submodule_name"; then
                        log_success "Submodule $submodule_name 更新完成"
                    else
                        log_error "更新 submodule $submodule_name 失败"
                    fi
                else
                    log_error "无效的 submodule 名称"
                fi
                wait_for_key
                ;;
            4)
                echo
                log_info "初始化并更新所有 submodules..."
                if git submodule init && git submodule update; then
                    log_success "Submodules 初始化完成"
                else
                    log_error "Submodules 初始化失败"
                fi
                wait_for_key
                ;;
            5)
                echo
                log_info "修复 submodule 问题..."
                echo "执行以下修复操作："
                echo "1. 同步 submodule URLs"
                echo "2. 重新初始化 submodules"
                echo "3. 更新到最新版本"

                if confirm_action "继续执行修复？" "Y"; then
                    git submodule sync
                    git submodule init
                    git submodule update --remote
                    log_success "Submodule 修复完成"
                fi
                wait_for_key
                ;;
            6)
                echo
                log_info ".gitmodules 配置:"
                if [ -f ".gitmodules" ]; then
                    cat .gitmodules
                else
                    echo "无 .gitmodules 文件"
                fi
                echo
                log_info "Submodule 详细信息:"
                git submodule foreach 'echo "=== $name ($sha1) ==="; echo "Path: $sm_path"; echo "URL: $(git remote get-url origin)"; echo "Branch: $(git branch --show-current)"; echo'
                wait_for_key
                ;;
            0)
                break
                ;;
            *)
                log_error "无效选择，请重新输入"
                sleep 1
                ;;
        esac
    done
}

# 显示使用指南
show_usage_guide() {
    echo -e "${CYAN}=== 📚 Git Submodules 使用指南 ===${NC}"

    echo
    echo "========================================"
    echo "🎯 什么是 Git Submodules？"
    echo "========================================"
    echo "Git Submodules 允许你将一个 Git 仓库作为另一个 Git 仓库的子目录。"
    echo "这样可以保持子项目的独立性，同时实现统一管理。"
    echo

    echo "========================================"
    echo "✅ 转换后的优势"
    echo "========================================"
    echo "• 🔒 子项目完全独立：可以独立开发、测试、部署"
    echo "• 🏠 统一项目管理：主项目可以统一部署和版本控制"
    echo "• 🔄 版本一致性：确保团队环境中子项目版本一致"
    echo "• 📦 简化部署：一键克隆获取完整项目结构"
    echo "• 🛡️ 避免冲突：消除版本控制冲突问题"
    echo

    echo "========================================"
    echo "🚀 常用操作命令"
    echo "========================================"
    echo "# 克隆包含 submodules 的项目"
    echo "git clone --recursive <repo-url>"
    echo
    echo "# 在已有项目中初始化 submodules"
    echo "git submodule init"
    echo "git submodule update"
    echo
    echo "# 更新所有 submodules 到最新版本"
    echo "git submodule update --remote"
    echo
    echo "# 更新指定 submodule"
    echo "git submodule update --remote <submodule-name>"
    echo
    echo "# 在 submodule 中工作"
    echo "cd <submodule-directory>"
    echo "git checkout <branch>"
    echo "# 正常的 git 操作..."
    echo "git add ."
    echo "git commit -m 'Update submodule'"
    echo "git push origin <branch>"
    echo
    echo "# 提交 submodule 更新到主项目"
    echo "cd .."
    echo "git add <submodule-directory>"
    echo "git commit -m 'Update submodule to latest version'"
    echo "git push"
    echo

    echo "========================================"
    echo "⚠️ 注意事项"
    echo "========================================"
    echo "• Submodule 默认处于 'detached HEAD' 状态"
    echo "• 在 submodule 中开发前，先切换到具体分支"
    echo "• 更新 submodule 后，记得在主项目中提交更改"
    echo "• 团队成员需要使用 --recursive 参数克隆项目"
    echo

    echo "========================================"
    echo "🔧 故障排除"
    echo "========================================"
    echo "问题：Submodule 目录为空"
    echo "解决：git submodule init && git submodule update"
    echo
    echo "问题：Submodule 更新失败"
    echo "解决：git submodule sync && git submodule update --remote"
    echo
    echo "问题：Submodule 处于错误状态"
    echo "解决：cd <submodule> && git checkout <correct-branch>"
    echo

    wait_for_key
}

# 主菜单
main_menu() {
    while true; do
        clear
        echo -e "${CYAN}========================================"
        echo -e "    🏠 Git Submodules 转换管理器"
        echo -e "========================================${NC}"
        echo
        echo -e "${WHITE}当前项目:${NC} $(basename "$WORKSPACE_DIR")"
        echo -e "${WHITE}工作目录:${NC} $WORKSPACE_DIR"
        echo
        echo -e "${WHITE}转换操作:${NC}"
        echo -e "${GREEN}1)${NC} 📊 分析当前项目结构    - 检查项目状态和转换可行性"
        echo -e "${GREEN}2)${NC} 🔄 执行 Submodules 转换 - 将子项目转换为 Git Submodules"
        echo -e "${GREEN}3)${NC} ✅ 验证转换结果        - 检查转换是否成功完成"
        echo
        echo -e "${WHITE}管理操作:${NC}"
        echo -e "${GREEN}4)${NC} 🔧 Submodules 日常管理  - 更新、同步、修复 submodules"
        echo -e "${GREEN}5)${NC} 📚 显示使用指南        - 学习 Git Submodules 用法"
        echo
        echo -e "${GREEN}0)${NC} 🚪 退出程序"
        echo
        read -p "请选择操作 [0-5]: " choice

        case $choice in
            1)
                analyze_current_structure
                wait_for_key
                ;;
            2)
                if analyze_current_structure; then
                    echo
                    execute_submodules_conversion
                else
                    log_error "项目结构分析失败，无法执行转换"
                fi
                wait_for_key
                ;;
            3)
                verify_conversion
                wait_for_key
                ;;
            4)
                manage_submodules
                ;;
            5)
                show_usage_guide
                ;;
            0)
                echo -e "${CYAN}感谢使用 Git Submodules 转换管理器！${NC}"
                exit 0
                ;;
            "")
                # 用户只按了回车键，不做任何操作
                ;;
            *)
                log_error "无效选择，请重新输入"
                sleep 2
                ;;
        esac
    done
}

# 主函数 - 命令行参数处理
main() {
    case "${1:-}" in
        "analyze"|"status")
            analyze_current_structure
            ;;
        "convert"|"transform")
            if analyze_current_structure; then
                echo
                execute_submodules_conversion
            else
                log_error "项目结构分析失败，无法执行转换"
                exit 1
            fi
            ;;
        "verify"|"check")
            verify_conversion
            ;;
        "manage"|"submodules")
            manage_submodules
            ;;
        "guide"|"help")
            show_usage_guide
            ;;
        "menu"|"interactive")
            main_menu
            ;;
        *)
            echo -e "${CYAN}=== 🏠 Git Submodules 转换管理器 ===${NC}"
            echo
            echo "这个脚本帮助你将混合项目转换为标准的 Git Submodules 结构。"
            echo
            echo "用法: $0 {analyze|convert|verify|manage|guide|menu}"
            echo
            echo "命令说明:"
            echo "  analyze   - 分析当前项目结构和转换可行性"
            echo "  convert   - 执行 Git Submodules 转换"
            echo "  verify    - 验证转换结果"
            echo "  manage    - Submodules 日常管理"
            echo "  guide     - 显示使用指南"
            echo "  menu      - 进入交互式菜单（推荐）"
            echo
            echo "快速开始:"
            echo "  1. 分析项目: $0 analyze"
            echo "  2. 执行转换: $0 convert"
            echo "  3. 验证结果: $0 verify"
            echo
            echo "或者直接运行交互式菜单:"
            echo "  $0 menu"
            echo
            echo -e "${YELLOW}注意：转换前请确保所有子项目都已提交并推送到远程仓库${NC}"
            ;;
    esac
}

# 如果直接执行则调用主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi