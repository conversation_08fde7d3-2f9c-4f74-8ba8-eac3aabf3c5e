#!/bin/bash

# New-API 子路径修复补丁
# 功能：修复 /ai 子路径相关的静态资源问题
# 版本：v1.0
# 应用场景：当应用部署在 /ai 子路径下时，修复前端资源路径问题

# 补丁信息
PATCH_NAME="子路径修复补丁"
PATCH_VERSION="v1.0"
PATCH_DESCRIPTION="修复 /ai 子路径下的静态资源路径问题"

# 应用补丁
apply_subpath_patch() {
    local base_dir="$1"
    local patches_applied=0
    
    echo "[PATCH] 应用 $PATCH_NAME ($PATCH_VERSION)"
    echo "[PATCH] 描述: $PATCH_DESCRIPTION"
    echo "[PATCH] 目标目录: $base_dir"
    
    cd "$base_dir" || return 1
    
    # 1. 修复 API 基础 URL
    echo "[PATCH] 1. 修复 API 基础 URL..."
    if [ -f "web/src/helpers/api.js" ]; then
        # 备份原文件
        cp web/src/helpers/api.js web/src/helpers/api.js.backup
        
        # 查找并替换 getApiBaseURL 函数
        if grep -q "getApiBaseURL" web/src/helpers/api.js; then
            sed -i '/function getApiBaseURL/,/^}/c\
// 动态获取API基础URL\
function getApiBaseURL() {\
  if (import.meta.env.VITE_REACT_APP_SERVER_URL) {\
    return import.meta.env.VITE_REACT_APP_SERVER_URL;\
  }\
\
  // 强制使用/ai作为基础路径，因为应用部署在/ai子路径下\
  return '\''/ai'\'';\
}' web/src/helpers/api.js
            
            if grep -q "return '/ai'" web/src/helpers/api.js; then
                echo "[PATCH] ✓ API 基础 URL 修复成功"
                ((patches_applied++))
            else
                echo "[PATCH] ✗ API 基础 URL 修复失败"
            fi
        else
            echo "[PATCH] ! 未找到 getApiBaseURL 函数"
        fi
    else
        echo "[PATCH] ! api.js 文件不存在"
    fi
    
    # 2. 修复 Logo 路径
    echo "[PATCH] 2. 修复 Logo 路径..."
    if [ -f "web/src/helpers/utils.js" ]; then
        cp web/src/helpers/utils.js web/src/helpers/utils.js.backup
        sed -i "s|return '/logo.png'|return '/ai/logo.png'|g" web/src/helpers/utils.js
        sed -i 's|return "/logo.png"|return "/ai/logo.png"|g' web/src/helpers/utils.js
        
        if grep -q "/ai/logo.png" web/src/helpers/utils.js; then
            echo "[PATCH] ✓ Logo 路径修复成功"
            ((patches_applied++))
        else
            echo "[PATCH] ✗ Logo 路径修复失败"
        fi
    else
        echo "[PATCH] ! utils.js 文件不存在"
    fi
    
    # 3. 修复 HTML 模板
    echo "[PATCH] 3. 修复 HTML 模板..."
    if [ -f "web/index.html" ]; then
        cp web/index.html web/index.html.backup
        sed -i 's|href="/logo.png"|href="/ai/logo.png"|g' web/index.html
        sed -i 's|src="/logo.png"|src="/ai/logo.png"|g' web/index.html
        
        if grep -q "/ai/logo.png" web/index.html; then
            echo "[PATCH] ✓ HTML 模板修复成功"
            ((patches_applied++))
        else
            echo "[PATCH] ✗ HTML 模板修复失败"
        fi
    else
        echo "[PATCH] ! index.html 文件不存在"
    fi
    
    # 4. 修复 Vite 配置
    echo "[PATCH] 4. 修复 Vite 配置..."
    if [ -f "web/vite.config.js" ]; then
        cp web/vite.config.js web/vite.config.js.backup
        
        if ! grep -q "base: '/ai/'" web/vite.config.js; then
            sed -i "s/base: '[^']*'/base: '\/ai\/'/" web/vite.config.js
            
            if grep -q "base: '/ai/'" web/vite.config.js; then
                echo "[PATCH] ✓ Vite 配置修复成功"
                ((patches_applied++))
            else
                echo "[PATCH] ✗ Vite 配置修复失败"
            fi
        else
            echo "[PATCH] ✓ Vite 配置已正确"
            ((patches_applied++))
        fi
    else
        echo "[PATCH] ! vite.config.js 文件不存在"
    fi
    
    # 5. 修复 React Router 配置
    echo "[PATCH] 5. 修复 React Router 配置..."
    if [ -f "web/src/index.js" ]; then
        cp web/src/index.js web/src/index.js.backup
        
        if ! grep -q 'basename="/ai"' web/src/index.js; then
            # 添加或修复 basename
            if grep -q "BrowserRouter" web/src/index.js; then
                sed -i 's|<BrowserRouter[^>]*>|<BrowserRouter basename="/ai">|g' web/src/index.js
            fi
            
            if grep -q 'basename="/ai"' web/src/index.js; then
                echo "[PATCH] ✓ React Router 配置修复成功"
                ((patches_applied++))
            else
                echo "[PATCH] ✗ React Router 配置修复失败"
            fi
        else
            echo "[PATCH] ✓ React Router 配置已正确"
            ((patches_applied++))
        fi
    else
        echo "[PATCH] ! index.js 文件不存在"
    fi
    
    echo "[PATCH] 补丁应用完成，成功应用 $patches_applied 个修复"
    return 0
}

# 验证补丁
verify_subpath_patch() {
    local base_dir="$1"
    local verification_passed=0
    local total_checks=5
    
    echo "[PATCH] 验证补丁应用结果..."
    
    cd "$base_dir" || return 1
    
    # 检查 API 基础 URL
    if grep -q "return '/ai'" web/src/helpers/api.js 2>/dev/null; then
        echo "[PATCH] ✓ API 基础 URL 配置正确"
        ((verification_passed++))
    else
        echo "[PATCH] ✗ API 基础 URL 配置错误"
    fi
    
    # 检查 Logo 路径
    if grep -q "/ai/logo.png" web/src/helpers/utils.js 2>/dev/null; then
        echo "[PATCH] ✓ Logo 路径配置正确"
        ((verification_passed++))
    else
        echo "[PATCH] ✗ Logo 路径配置错误"
    fi
    
    # 检查 HTML 模板
    if grep -q "/ai/logo.png" web/index.html 2>/dev/null; then
        echo "[PATCH] ✓ HTML 模板配置正确"
        ((verification_passed++))
    else
        echo "[PATCH] ✗ HTML 模板配置错误"
    fi
    
    # 检查 Vite 配置
    if grep -q "base: '/ai/'" web/vite.config.js 2>/dev/null; then
        echo "[PATCH] ✓ Vite 配置正确"
        ((verification_passed++))
    else
        echo "[PATCH] ✗ Vite 配置错误"
    fi
    
    # 检查 React Router 配置
    if grep -q 'basename="/ai"' web/src/index.js 2>/dev/null; then
        echo "[PATCH] ✓ React Router 配置正确"
        ((verification_passed++))
    else
        echo "[PATCH] ✗ React Router 配置错误"
    fi
    
    echo "[PATCH] 验证结果: $verification_passed/$total_checks 通过"
    
    if [ $verification_passed -eq $total_checks ]; then
        echo "[PATCH] 所有验证通过，补丁应用成功！"
        return 0
    else
        echo "[PATCH] 部分验证失败，请检查上述错误"
        return 1
    fi
}

# 如果直接执行此脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case "${1:-help}" in
        "apply")
            apply_subpath_patch "${2:-/root/workspace/new-api}"
            ;;
        "verify")
            verify_subpath_patch "${2:-/root/workspace/new-api}"
            ;;
        "help"|"")
            echo "用法: $0 <命令> [目录]"
            echo "命令:"
            echo "  apply [目录]   - 应用子路径修复补丁"
            echo "  verify [目录]  - 验证补丁应用结果"
            echo "  help          - 显示帮助信息"
            ;;
        *)
            echo "未知命令: $1"
            exit 1
            ;;
    esac
fi
