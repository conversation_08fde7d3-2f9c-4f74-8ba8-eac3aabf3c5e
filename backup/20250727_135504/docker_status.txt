NAME      IMAGE             COMMAND                  SERVICE   CREATED         STATUS                   PORTS
mysql     mysql:8.2         "docker-entrypoint.s…"   mysql     3 minutes ago   Up 3 minutes             3306/tcp, 33060/tcp
new-api   new-api-new-api   "/one-api --log-dir …"   new-api   3 minutes ago   Up 2 minutes (healthy)   0.0.0.0:3000->3000/tcp, [::]:3000->3000/tcp
redis     redis:latest      "docker-entrypoint.s…"   redis     3 minutes ago   Up 3 minutes             6379/tcp
