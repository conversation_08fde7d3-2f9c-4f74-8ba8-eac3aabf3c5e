# 限流配置

# 定义限流区域（在http块中定义）
# limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
# limit_req_zone $binary_remote_addr zone=api:10m rate=5r/s;
# limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

# 连接数限制
# limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

# 应用限流规则
# 一般页面限流
limit_req zone=general burst=20 nodelay;

# API接口限流
location /api/ {
    limit_req zone=api burst=10 nodelay;
    limit_req_status 429;
}

# 登录接口严格限流
location /api/user/login {
    limit_req zone=login burst=3 nodelay;
    limit_req_status 429;
}

# 连接数限制
limit_conn conn_limit_per_ip 20;

# 限流日志
error_log /var/log/nginx/rate_limit.log warn;
