NAME      IMAGE             COMMAND                  SERVICE   CREATED              STATUS                    PORTS
mysql     mysql:8.2         "docker-entrypoint.s…"   mysql     About a minute ago   Up About a minute         3306/tcp, 33060/tcp
new-api   new-api-new-api   "/one-api --log-dir …"   new-api   About a minute ago   Up 56 seconds (healthy)   0.0.0.0:3000->3000/tcp, [::]:3000->3000/tcp
redis     redis:latest      "docker-entrypoint.s…"   redis     About a minute ago   Up About a minute         6379/tcp
