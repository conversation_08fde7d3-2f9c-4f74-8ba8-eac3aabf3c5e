# Love静态文件配置

# 静态资源缓存设置
location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 字体文件特殊处理
location ~* \.(ttf|otf|woff|woff2|eot)$ {
    add_header Access-Control-Allow-Origin "*";
    add_header Cache-Control "public, max-age=31536000, immutable";
    expires 1y;
}

# 媒体文件处理（视频、大图片等）
location ~* \.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    # 支持大文件流式传输
    add_header Accept-Ranges bytes;
}

# HTML文件不缓存（动态内容）
location ~* \.html$ {
    add_header Cache-Control "no-cache, no-store, must-revalidate";
    add_header Pragma "no-cache";
    add_header Expires "0";
} 