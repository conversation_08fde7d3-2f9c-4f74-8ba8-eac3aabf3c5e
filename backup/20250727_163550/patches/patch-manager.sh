#!/bin/bash

# New-API 补丁管理器
# 功能：统一管理所有补丁的应用、验证和状态检查
# 版本：v1.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NEW_API_DIR="/root/workspace/new-api"
PATCHES_DIR="$NEW_API_DIR/patches"

# 可用补丁列表
AVAILABLE_PATCHES=(
    "subpath-fix.patch:子路径修复补丁:修复/ai子路径下的静态资源问题"
    "gemini-api-fix.patch:Gemini API端点修复:修复Gemini API模型列表端点"
)

# 检查补丁目录
check_patches_dir() {
    if [ ! -d "$PATCHES_DIR" ]; then
        log_error "补丁目录不存在: $PATCHES_DIR"
        return 1
    fi
    
    log_success "补丁目录检查通过: $PATCHES_DIR"
    return 0
}

# 列出所有可用补丁
list_patches() {
    log_info "可用补丁列表:"
    echo "========================================"
    
    local index=1
    for patch_info in "${AVAILABLE_PATCHES[@]}"; do
        IFS=':' read -r patch_file patch_name patch_desc <<< "$patch_info"
        
        echo -e "${CYAN}$index) $patch_name${NC}"
        echo "   文件: $patch_file"
        echo "   描述: $patch_desc"
        
        # 检查补丁文件是否存在
        if [ -f "$PATCHES_DIR/$patch_file" ]; then
            echo -e "   状态: ${GREEN}✓ 可用${NC}"
        else
            echo -e "   状态: ${RED}✗ 文件不存在${NC}"
        fi
        
        echo
        ((index++))
    done
}

# 应用单个补丁
apply_single_patch() {
    local patch_file="$1"
    
    if [ ! -f "$PATCHES_DIR/$patch_file" ]; then
        log_error "补丁文件不存在: $patch_file"
        return 1
    fi
    
    log_info "应用补丁: $patch_file"
    
    # 使补丁文件可执行
    chmod +x "$PATCHES_DIR/$patch_file"
    
    # 执行补丁
    if "$PATCHES_DIR/$patch_file" apply "$NEW_API_DIR"; then
        log_success "补丁应用成功: $patch_file"
        return 0
    else
        log_error "补丁应用失败: $patch_file"
        return 1
    fi
}

# 验证单个补丁
verify_single_patch() {
    local patch_file="$1"
    
    if [ ! -f "$PATCHES_DIR/$patch_file" ]; then
        log_error "补丁文件不存在: $patch_file"
        return 1
    fi
    
    log_info "验证补丁: $patch_file"
    
    # 使补丁文件可执行
    chmod +x "$PATCHES_DIR/$patch_file"
    
    # 验证补丁
    if "$PATCHES_DIR/$patch_file" verify "$NEW_API_DIR"; then
        log_success "补丁验证通过: $patch_file"
        return 0
    else
        log_error "补丁验证失败: $patch_file"
        return 1
    fi
}

# 应用所有补丁
apply_all_patches() {
    log_info "开始应用所有补丁..."
    
    local applied_count=0
    local failed_count=0
    
    for patch_info in "${AVAILABLE_PATCHES[@]}"; do
        IFS=':' read -r patch_file patch_name patch_desc <<< "$patch_info"
        
        echo "========================================"
        log_info "处理补丁: $patch_name"
        
        if apply_single_patch "$patch_file"; then
            ((applied_count++))
        else
            ((failed_count++))
        fi
    done
    
    echo "========================================"
    log_info "补丁应用完成"
    log_info "成功: $applied_count 个"
    log_info "失败: $failed_count 个"
    
    if [ $failed_count -eq 0 ]; then
        log_success "所有补丁应用成功！"
        return 0
    else
        log_warning "部分补丁应用失败，请检查上述错误"
        return 1
    fi
}

# 验证所有补丁
verify_all_patches() {
    log_info "开始验证所有补丁..."
    
    local verified_count=0
    local failed_count=0
    
    for patch_info in "${AVAILABLE_PATCHES[@]}"; do
        IFS=':' read -r patch_file patch_name patch_desc <<< "$patch_info"
        
        echo "========================================"
        log_info "验证补丁: $patch_name"
        
        if verify_single_patch "$patch_file"; then
            ((verified_count++))
        else
            ((failed_count++))
        fi
    done
    
    echo "========================================"
    log_info "补丁验证完成"
    log_info "通过: $verified_count 个"
    log_info "失败: $failed_count 个"
    
    if [ $failed_count -eq 0 ]; then
        log_success "所有补丁验证通过！"
        return 0
    else
        log_warning "部分补丁验证失败，请检查上述错误"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "New-API 补丁管理器"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  list                    - 列出所有可用补丁"
    echo "  apply <补丁文件>        - 应用指定补丁"
    echo "  apply-all               - 应用所有补丁"
    echo "  verify <补丁文件>       - 验证指定补丁"
    echo "  verify-all              - 验证所有补丁"
    echo "  help                    - 显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 list                                 # 列出所有补丁"
    echo "  $0 apply subpath-fix.patch              # 应用子路径修复补丁"
    echo "  $0 apply-all                            # 应用所有补丁"
    echo "  $0 verify gemini-api-fix.patch          # 验证Gemini补丁"
    echo "  $0 verify-all                           # 验证所有补丁"
}

# 主函数
main() {
    # 检查补丁目录
    if ! check_patches_dir; then
        exit 1
    fi
    
    case "${1:-help}" in
        "list")
            list_patches
            ;;
        "apply")
            if [ -z "$2" ]; then
                log_error "请指定要应用的补丁文件"
                show_help
                exit 1
            fi
            apply_single_patch "$2"
            ;;
        "apply-all")
            apply_all_patches
            ;;
        "verify")
            if [ -z "$2" ]; then
                log_error "请指定要验证的补丁文件"
                show_help
                exit 1
            fi
            verify_single_patch "$2"
            ;;
        "verify-all")
            verify_all_patches
            ;;
        "help"|"")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
