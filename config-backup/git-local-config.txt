# Git本地仓库配置备份 - 2025-07-27 17:28:17
# 注意：敏感信息已被脱敏处理

[core]
	repositoryformatversion = 0
	filemode = true
	bare = false
	logallrefupdates = true
[remote "origin"]
	url = https:***@github.com/CoolKingW/workspace.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
	vscode-merge-base = origin/master
[lfs]
	repositoryformatversion = 0
[lfs "https://github.com/CoolKingW/workspace.git/info/lfs"]
	access = basic
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
