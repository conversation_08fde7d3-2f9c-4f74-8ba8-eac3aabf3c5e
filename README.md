# 🏠 Workspace 综合项目工作空间

🚀 **一键部署** | 🤖 **AI API网关** | 💕 **情侣网站** | 🔄 **AI代理服务** | 🔧 **统一管理**

这是一个多项目集成的工作空间，包含New-API（AI API网关系统）、Love（情侣网站项目）和GPT-Load（AI代理服务），支持在全新环境中一键部署，各子项目独立管理，采用现代化的模块化nginx架构。

## 🎯 项目概述

### 📂 项目结构
```
workspace/
├── 🤖 new-api/          # AI API网关项目
├── 💕 love/             # 情侣网站项目
├── 🔄 gpt-load/         # GPT-Load AI代理服务 ✨ 新增
├── 🔧 shared/           # 共享资源和工具
│   └── nginx/           # ✨ 新Nginx架构（模块化配置管理）
├── 📄 README.md         # 本文件
├── 📄 guide.md          # AI助手指南
├── 📄 nginx.md          # ✨ Nginx架构文档
└── 📄 deploy.sh         # 综合部署脚本
```

### 🌟 核心项目

#### 🤖 New-API 项目
- **功能**: AI API网关和管理系统
- **技术栈**: Go + React + Docker + MySQL + Redis
- **特点**: 支持多种AI模型，统一API管理
- **文档**: [new-api/README.md](./new-api/README.md)

#### 💕 Love 项目
- **功能**: 浪漫的情侣专用网站
- **技术栈**: Node.js + Express + SQLite + HTML/CSS/JS
- **特点**: 消息留言、互动功能
- **文档**: [love/README.md](./love/README.md)

#### 🔄 GPT-Load 项目 ✨ **新增**
- **功能**: 高性能AI接口透明代理服务
- **技术栈**: Go 1.24.3 + Gin Framework + Vue.js 3 + TypeScript + SQLite
- **特点**: 子路径部署、嵌入式前端、智能配置管理
- **访问**: https://liangliangdamowang.edu.deal/lgpt-load/
- **文档**: [gpt-load/guide-gptload.md](./gpt-load/guide-gptload.md)

#### 🔧 Shared 共享资源
- **功能**: 跨项目的脚本、配置和工具
- **内容**: 管理脚本、Nginx配置、SSL证书配置
- **✨ 新Nginx架构**: 模块化配置管理系统
  - **conf.d/**: 项目配置文件
  - **snippets/**: 可复用配置片段
  - **templates/**: 配置模板
  - **scripts/**: 自动化管理脚本
  - **backup/**: 完整配置备份
- **文档**: [shared/README.md](./shared/README.md) | [nginx.md](./nginx.md)

## 🚀 快速开始

### 🌟 全新环境一键部署（推荐）

适用于全新的服务器环境，自动安装所有依赖并配置完整系统：

```bash
# 1. 克隆项目
git clone https://github.com/CoolKingW/workspace.git
cd workspace

# 2. 一键部署所有项目（自动安装Docker、Nginx、Node.js等）
sudo ./deploy.sh

# 3. 部署完成后访问
# - New-API: https://yourdomain.com
# - Love网站: https://yourdomain.com/love/
# - GPT-Load: https://yourdomain.com/lgpt-load/ ✨ 新增
# - AI路径: https://yourdomain.com/ai/ ✨ 新增
```

**注意**: 部署脚本会自动处理所有依赖安装、服务配置和SSL证书申请。

### 📋 分步部署

如果您需要分别部署各个项目：

#### 检查系统环境
```bash
./check-environment.sh
```

#### 仅检查部署环境
```bash
sudo ./deploy.sh --check
```

#### 仅部署New-API项目
```bash
sudo ./deploy.sh --new-api
# 或者
cd new-api && sudo ./cmd.sh
```

#### 仅部署情侣网站项目
```bash
sudo ./deploy.sh --love
# 或者
cd love && ./deploy-love-backend.sh
```

#### 查看部署帮助
```bash
./deploy.sh --help
```

## 📋 项目管理

### 🎯 管理原则
- **根目录**: 只有一个 `deploy.sh` 一键部署脚本
- **子项目管理**: 通过各自的管理脚本进行独立管理
- **配置统一**: 所有配置通过子项目管理脚本统一处理

### New-API 项目管理
```bash
cd new-api
./new-api-manager.sh          # 交互式管理界面
./new-api-manager.sh status   # 检查状态
./new-api-manager.sh restart  # 重启服务
./new-api-manager.sh backup   # 备份数据库
```

### love 项目管理
```bash
cd love
./manage.sh                   # 交互式管理界面
./manage.sh status            # 检查状态
./manage.sh start             # 启动服务
./manage.sh stop              # 停止服务
```

### 🔧 管理功能
Love项目管理脚本 (`./love/manage.sh`) 包含：
- **服务管理**: 启动、停止、重启Love服务
- **部署管理**: 生产环境部署、Nginx配置
- **数据库管理**: 备份、恢复、统计
- **监控管理**: 实时监控、性能检查
- **故障排除**: 自动诊断和修复
- **系统验证**: 验证所有服务状态、测试外部访问

## 🔧 开发指南

### New-API 开发
- 进入 `new-api/` 目录
- 查看 [new-api/README.md](./new-api/README.md) 了解详细信息
- 使用 Docker 进行开发和部署

### love 开发
- 进入 `love/` 目录
- 前端文件: `index.html`, `style.css`, `script.js`
- 后端文件: `server.js`
- 使用 `./start-backend.sh` 启动开发服务器

### 共享工具开发
- 进入 `shared/` 目录
- 添加通用脚本到 `scripts/` 目录
- 添加共享配置到相应目录

## 🌐 网络架构

### 域名配置
- **主域名**: `liangliangdamowang.edu.deal`（示例）
- **SSL证书**: Let's Encrypt 自动续期
- **反向代理**: Nginx 统一管理（通过Love项目配置）

### 服务映射
```
https://yourdomain.com/
├── /                    → New-API 服务 (端口 3000) ✅ 主要功能
├── /new-api/            → New-API 服务 (向后兼容路径)
├── /ai/                 → AI路径前端 ✨ 新增功能
├── /ai/api/             → AI路径API ✨ 新增功能
├── /love/               → Love 网站 (静态文件) ✅ 情侣网站
├── /love/api/           → Love API (端口 1314)
├── /love/style.css      → Love 样式文件
├── /love/script.js      → Love 脚本文件
├── /love/background/    → Love 背景资源
├── /lgpt-load/          → GPT-Load 代理服务 (端口 3001) ✨ 新增
├── /lgpt-load/api/      → GPT-Load API ✨ 新增
└── /lgpt-load/assets/   → GPT-Load 静态资源 ✨ 新增
```

### 🌐 网络架构 ✨ **新架构升级**

#### 配置文件架构
```
✨ 新模块化架构（当前使用）：
1. 生产配置: /etc/nginx/conf.d/liangliangdamowang.edu.deal.conf
2. 配置片段: shared/nginx/snippets/*.conf (SSL、代理、安全头等)
3. 配置模板: shared/nginx/templates/*.tpl (项目模板)
4. 管理脚本: deploy-all/scripts/nginx-manager.sh

传统架构（备份保留）：
1. 备用配置: shared/nginx/system-nginx.conf (故障恢复)
2. 历史配置: shared/nginx/default.conf (仅作参考)
```

#### 新架构特性
- **模块化配置**: 使用snippets实现配置复用，避免重复代码
- **自动化管理**: nginx-manager.sh提供完整的项目管理功能
- **模板化部署**: 支持快速添加新项目和SSL证书申请
- **配置备份**: 完整的配置备份和恢复机制
- **智能冲突检测**: 自动检测和解决配置冲突

#### 管理工具
- **主要工具**: `./deploy-all/scripts/nginx-manager.sh`
- **架构文档**: `./nginx.md` - 完整的使用指南
- **项目管理**: 支持添加项目、启用/禁用站点、SSL证书管理

### SSL 证书配置
```bash
# 自动配置（部署脚本会处理）
sudo certbot --nginx -d yourdomain.com

# 手动配置
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com
```

## 📦 依赖要求

### 🖥️ 系统要求

#### 最低配置
- **操作系统**: Ubuntu 18.04+ / Debian 10+ / CentOS 7+
- **内存**: 1GB RAM (推荐 2GB+)
- **磁盘**: 5GB 可用空间 (推荐 10GB+)
- **CPU**: 1核 (推荐 2核+)
- **网络**: 稳定的互联网连接

#### 推荐配置
- **操作系统**: Ubuntu 22.04 LTS
- **内存**: 4GB RAM
- **磁盘**: 20GB SSD
- **CPU**: 2核+
- **网络**: 带宽 10Mbps+

### 🔧 软件依赖

#### 自动安装的依赖
部署脚本会自动安装以下软件：
- **Docker** & **Docker Compose** - 容器化部署
- **Nginx** - Web服务器和反向代理
- **Node.js 18+** - 用于love项目
- **Certbot** - SSL证书管理
- **基础工具**: curl, wget, git, unzip等

#### 手动安装（可选）
如果您希望手动安装依赖：
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y curl wget git unzip nginx nodejs npm

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### ✅ 环境检测
使用内置的环境检测工具检查系统是否满足要求：
```bash
./check-environment.sh
```

## 🎛️ 管理命令 ✨ **新增功能**

### Nginx配置管理
```bash
# 查看所有nginx管理功能
./deploy-all/scripts/nginx-manager.sh --help

# 查看系统状态
./deploy-all/scripts/nginx-manager.sh --status

# 添加新项目
./deploy-all/scripts/nginx-manager.sh --add-project example.com myapp project

# 查看可用模板
./deploy-all/scripts/nginx-manager.sh --list-templates

# 启用/禁用站点
./deploy-all/scripts/nginx-manager.sh --enable-site example.com
./deploy-all/scripts/nginx-manager.sh --disable-site example.com

# SSL证书管理
./deploy-all/scripts/nginx-manager.sh --ssl-cert example.com

# 配置备份和测试
./deploy-all/scripts/nginx-manager.sh --backup
./deploy-all/scripts/nginx-manager.sh --test
```

### 项目管理
```bash
# New-API管理
cd new-api && ./new-api-manager.sh

# Love项目管理
cd love && ./manage.sh

# GPT-Load管理 ✨ 新增
cd gpt-load && ./manage-gptload.sh

# 一键部署
sudo ./deploy.sh
```

## 🛠️ 故障排除

### 检查服务状态
```bash
# New-API 服务
cd new-api && ./new-api-manager.sh status

# Love 服务
cd love && ps aux | grep node

# GPT-Load 服务 ✨ 新增
cd gpt-load && ./manage-gptload.sh service status

# Nginx架构状态 ✨ 新增
./deploy-all/scripts/nginx-manager.sh --status
./deploy-all/scripts/nginx-manager.sh --test

# 共享服务检查
./shared/scripts/workspace-manager.sh status
```

### 查看日志
```bash
# New-API 日志
cd new-api && docker-compose logs -f

# love 日志
cd love && tail -f backend.log

# Nginx 日志
sudo tail -f /var/log/nginx/error.log
```

## 📚 文档索引

- 📖 [AI助手指南](./guide.md) - AI助手专用操作指南
- 🤖 [New-API文档](./new-api/README.md) - AI API网关详细文档
- 💕 [love文档](./love/README.md) - 情侣网站项目文档
- 🔧 [Shared文档](./shared/README.md) - 共享资源说明

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🎯 项目优势

- 🏗️ **模块化架构**: 独立项目，统一管理
- 🚀 **快速部署**: 一键部署脚本，自动化配置
- 🔧 **灵活管理**: 项目级和工作空间级管理工具
- ✨ **新Nginx架构**: 模块化配置管理，snippets复用，自动化项目添加
- 🌐 **多项目支持**: New-API、Love、GPT-Load三个项目完整集成
- 📦 **完整备份**: 自动备份和恢复机制，配置版本控制
- 🛡️ **安全可靠**: SSL证书、权限管理、数据保护，现代安全配置
- 📚 **文档完善**: 详细的使用指南和故障排除，包含nginx.md架构文档
- 🎯 **扩展性强**: 模板化配置，快速添加新项目，智能冲突检测

---

**🌟 享受多项目集成的开发和部署体验！** 🎉