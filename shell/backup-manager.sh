#!/bin/bash

# 💾 备份管理模块 - 备份推送系统
# 版本：v2.0
# 功能：备份管理和历史清理

# 加载依赖
source "$(dirname "${BASH_SOURCE[0]}")/config.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"
source "$(dirname "${BASH_SOURCE[0]}")/git-operations.sh"

# 分析当前项目状态
analyze_project_status() {
    log_info "=== 📊 分析项目状态 ==="
    
    cd "$WORKSPACE_DIR"
    
    # 检查主项目状态
    echo "========================================"
    echo "主项目状态"
    echo "========================================"
    
    if ! check_git_repo; then
        log_error "主项目不是Git仓库"
        return 1
    fi
    
    get_git_info
    echo
    
    # 检查子项目状态
    echo "========================================"
    echo "子项目状态"
    echo "========================================"
    
    local all_subprojects_ok=true
    for project in "${!SUBPROJECTS[@]}"; do
        echo "--- $project ---"
        
        if [ ! -d "$project" ]; then
            log_error "子项目目录不存在: $project"
            all_subprojects_ok=false
            continue
        fi
        
        if [ -d "$project/.git" ]; then
            log_success "子项目有独立Git仓库: $project"
            get_git_info "$project"
        else
            log_warning "子项目无Git仓库: $project"
        fi
        echo
    done
    
    # 检查Git跟踪状态
    echo "========================================"
    echo "Git跟踪状态分析"
    echo "========================================"
    
    local tracking_subproject_files=false
    for project in "${!SUBPROJECTS[@]}"; do
        local tracked_count=$(git ls-files "$project/" 2>/dev/null | wc -l)
        if [ "$tracked_count" -gt 0 ]; then
            log_warning "主项目正在跟踪 $project/ 下的 $tracked_count 个文件"
            tracking_subproject_files=true
        else
            log_success "主项目未跟踪 $project/ 文件"
        fi
    done
    
    # 检查大文件
    echo
    echo "========================================"
    echo "大文件检查"
    echo "========================================"
    
    check_large_files "$WORKSPACE_DIR" 50
    
    # 检查LFS状态
    if [ "$LFS_ENABLED" = "true" ]; then
        echo
        echo "========================================"
        echo "Git LFS状态"
        echo "========================================"
        
        if command -v git-lfs >/dev/null 2>&1; then
            if [ -f ".gitattributes" ]; then
                log_success "Git LFS已配置"
                echo "LFS跟踪的文件类型:"
                grep "filter=lfs" .gitattributes | sed 's/^/  /'
                
                local lfs_files=$(git lfs ls-files | wc -l)
                echo "当前LFS文件数: $lfs_files"
            else
                log_warning "Git LFS未配置"
            fi
        else
            log_error "Git LFS未安装"
        fi
    fi
    
    # 总结
    echo
    echo "========================================"
    echo "状态总结"
    echo "========================================"
    
    if [ "$tracking_subproject_files" = true ]; then
        log_warning "检测到问题：主项目正在跟踪子项目文件"
        echo "  建议：清理历史跟踪记录，重新建立干净的备份"
    else
        log_success "主项目跟踪状态正常"
    fi
    
    if [ "$all_subprojects_ok" = true ]; then
        log_success "所有子项目状态正常"
    else
        log_warning "部分子项目存在问题"
    fi
    
    return 0
}

# 清理历史跟踪记录
clean_history_tracking() {
    log_info "=== 🧹 清理历史跟踪记录 ==="
    
    cd "$WORKSPACE_DIR"
    
    # 确认操作
    echo "此操作将："
    echo "1. 从Git历史中移除所有子项目文件的跟踪记录"
    echo "2. 保留子项目的当前文件内容"
    echo "3. 创建全新的干净历史"
    echo "4. 不影响子项目的独立Git仓库"
    echo
    
    if ! confirm_action "确定要清理历史跟踪记录吗？" "N"; then
        log_info "操作已取消"
        return 1
    fi
    
    # 备份当前状态
    local backup_branch="backup-before-cleanup-$(date +%Y%m%d_%H%M%S)"
    git branch "$backup_branch"
    log_success "创建备份分支: $backup_branch"
    
    # 创建临时分支进行清理
    local cleanup_branch="cleanup-$(date +%Y%m%d_%H%M%S)"
    git checkout -b "$cleanup_branch"
    
    # 移除所有子项目文件的跟踪
    log_info "移除子项目文件跟踪..."
    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -1 | grep -q .; then
            log_info "移除 $project/ 的跟踪记录"
            git rm -r --cached "$project/" 2>/dev/null || true
        fi
    done
    
    # 提交清理更改
    if ! git diff --cached --quiet; then
        git commit -m "Clean: Remove subproject tracking from history

This commit removes all tracking of subproject files from the main
repository history. Subprojects will be managed as independent
repositories while maintaining their content for backup purposes.

Cleaned projects: $(echo "${!SUBPROJECTS[@]}" | tr ' ' ', ')
Cleanup date: $(date -Iseconds)"
        
        log_success "历史清理提交完成"
    else
        log_info "没有需要清理的跟踪记录"
    fi
    
    # 切换回主分支并合并
    git checkout master
    git merge "$cleanup_branch" --no-ff -m "Merge history cleanup

Merged clean history without subproject tracking.
This establishes a clean baseline for backup operations."
    
    # 删除临时分支
    git branch -d "$cleanup_branch"
    
    log_success "历史跟踪记录清理完成"
    return 0
}

# 建立新的备份基线
establish_backup_baseline() {
    log_info "=== 📋 建立备份基线 ==="
    
    cd "$WORKSPACE_DIR"
    
    # 确保.gitignore正确配置
    log_info "配置.gitignore..."
    create_default_gitignore
    
    # 初始化Git LFS（如果启用）
    if [ "$LFS_ENABLED" = "true" ]; then
        log_info "初始化Git LFS..."
        init_git_lfs
    fi
    
    # 智能添加当前文件
    log_info "添加当前项目文件..."
    
    # 添加主项目文件
    git add README.md git.sh guide.md 2>/dev/null || true
    git add shell/ 2>/dev/null || true
    git add shared/ 2>/dev/null || true
    git add .gitignore .gitattributes 2>/dev/null || true
    
    # 添加子项目内容（排除.git目录）
    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            log_info "添加子项目内容: $project"
            
            # 使用find排除.git目录
            find "$project" -type f -not -path "*/.git/*" -print0 | while IFS= read -r -d '' file; do
                # 检查是否应该忽略
                if ! should_ignore_file "$file"; then
                    git add "$file"
                fi
            done
        fi
    done
    
    # 检查添加的文件
    local added_files=$(git diff --cached --name-only | wc -l)
    log_info "准备提交 $added_files 个文件"
    
    if [ "$added_files" -eq 0 ]; then
        log_warning "没有文件需要提交"
        return 0
    fi
    
    # 显示将要提交的文件概览
    echo "将要提交的文件类型统计:"
    git diff --cached --name-only | sed 's/.*\.//' | sort | uniq -c | sort -nr | head -10
    
    # 提交基线
    local baseline_message="Establish backup baseline

This commit establishes a clean backup baseline containing:
- Main project files and configuration
- Current state of all subprojects (content only)
- Proper .gitignore and LFS configuration

Subprojects included: $(echo "${!SUBPROJECTS[@]}" | tr ' ' ', ')
Baseline date: $(date -Iseconds)
Total files: $added_files

Note: Subproject .git directories are excluded to maintain
their independence while preserving complete backup capability."
    
    if commit_changes "$WORKSPACE_DIR" "$baseline_message" false; then
        log_success "备份基线建立完成"
        
        # 显示统计信息
        get_repo_stats
        
        return 0
    else
        log_error "备份基线建立失败"
        return 1
    fi
}

# 执行完整备份
perform_full_backup() {
    log_info "=== 💾 执行完整备份 ==="
    
    cd "$WORKSPACE_DIR"
    
    # 检查磁盘空间
    if ! check_disk_space; then
        return 1
    fi
    
    # 分析当前状态
    analyze_project_status
    
    echo
    if ! confirm_action "开始执行完整备份？" "Y"; then
        log_info "备份已取消"
        return 1
    fi
    
    # 步骤1: 清理历史（如果需要）
    local has_old_tracking=false
    for project in "${!SUBPROJECTS[@]}"; do
        if git ls-files "$project/" | head -1 | grep -q .; then
            has_old_tracking=true
            break
        fi
    done
    
    if [ "$has_old_tracking" = true ]; then
        echo
        log_warning "检测到旧的跟踪记录"
        if confirm_action "是否清理历史跟踪记录？" "Y"; then
            clean_history_tracking
        fi
    fi
    
    # 步骤2: 建立备份基线
    echo
    log_info "建立备份基线..."
    establish_backup_baseline
    
    # 步骤3: 推送到远程
    echo
    log_info "推送备份到远程仓库..."
    if push_to_remote; then
        log_success "完整备份完成！"
        
        # 发送成功通知
        send_notification "success" "完整备份成功完成，包含所有子项目的最新状态"
        
        return 0
    else
        log_error "备份推送失败"
        send_notification "error" "备份推送失败，请检查网络连接和权限"
        return 1
    fi
}

# 增量备份更新
perform_incremental_backup() {
    log_info "=== 🔄 执行增量备份 ==="
    
    cd "$WORKSPACE_DIR"
    
    # 检查是否有更改
    local has_changes=false
    
    # 检查主项目更改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        has_changes=true
        log_info "检测到主项目更改"
    fi
    
    # 检查子项目更改
    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            # 比较工作目录与最后提交的差异
            local project_changes=$(find "$project" -type f -not -path "*/.git/*" -newer "$project" 2>/dev/null | wc -l)
            if [ "$project_changes" -gt 0 ]; then
                has_changes=true
                log_info "检测到 $project 项目更改"
            fi
        fi
    done
    
    if [ "$has_changes" = false ]; then
        log_info "没有检测到更改，跳过备份"
        return 0
    fi
    
    # 智能添加更改的文件
    smart_git_add
    
    # 提交增量更改
    commit_changes "$WORKSPACE_DIR" "" true
    
    # 推送更改
    if push_to_remote; then
        log_success "增量备份完成"
        send_notification "success" "增量备份成功完成"
        return 0
    else
        log_error "增量备份失败"
        send_notification "error" "增量备份失败"
        return 1
    fi
}

# 验证备份完整性
verify_backup_integrity() {
    log_info "=== ✅ 验证备份完整性 ==="
    
    cd "$WORKSPACE_DIR"
    
    local errors=0
    
    # 检查Git仓库完整性
    log_info "检查Git仓库完整性..."
    if git fsck --full --strict; then
        log_success "Git仓库完整性检查通过"
    else
        log_error "Git仓库完整性检查失败"
        errors=$((errors + 1))
    fi
    
    # 检查远程同步状态
    log_info "检查远程同步状态..."
    local local_commit=$(git rev-parse HEAD)
    local remote_commit=$(git rev-parse origin/$(git branch --show-current) 2>/dev/null || echo "unknown")
    
    if [ "$local_commit" = "$remote_commit" ]; then
        log_success "本地与远程同步"
    else
        log_warning "本地与远程不同步"
        log_info "本地: $local_commit"
        log_info "远程: $remote_commit"
    fi
    
    # 检查子项目完整性
    log_info "检查子项目完整性..."
    for project in "${!SUBPROJECTS[@]}"; do
        if [ -d "$project" ]; then
            local project_files=$(find "$project" -type f -not -path "*/.git/*" | wc -l)
            if [ "$project_files" -gt 0 ]; then
                log_success "$project: $project_files 个文件"
            else
                log_error "$project: 没有文件"
                errors=$((errors + 1))
            fi
        else
            log_error "$project: 目录不存在"
            errors=$((errors + 1))
        fi
    done
    
    # 检查LFS文件（如果启用）
    if [ "$LFS_ENABLED" = "true" ] && command -v git-lfs >/dev/null 2>&1; then
        log_info "检查LFS文件完整性..."
        if git lfs fsck; then
            log_success "LFS文件完整性检查通过"
        else
            log_error "LFS文件完整性检查失败"
            errors=$((errors + 1))
        fi
    fi
    
    # 总结
    if [ $errors -eq 0 ]; then
        log_success "备份完整性验证通过"
        return 0
    else
        log_error "发现 $errors 个完整性问题"
        return 1
    fi
}

# 恢复备份
restore_from_backup() {
    local backup_ref="${1:-HEAD}"
    
    log_info "=== 🔄 从备份恢复 ==="
    
    cd "$WORKSPACE_DIR"
    
    log_warning "此操作将恢复到指定的备份状态: $backup_ref"
    log_warning "当前未提交的更改将会丢失！"
    
    if ! confirm_action "确定要继续恢复吗？" "N"; then
        log_info "恢复操作已取消"
        return 1
    fi
    
    # 创建恢复前的备份
    local pre_restore_branch="pre-restore-$(date +%Y%m%d_%H%M%S)"
    git branch "$pre_restore_branch"
    log_info "创建恢复前备份分支: $pre_restore_branch"
    
    # 执行恢复
    if git reset --hard "$backup_ref"; then
        log_success "恢复完成"
        
        # 清理工作目录中的未跟踪文件
        if confirm_action "是否清理未跟踪的文件？" "Y"; then
            git clean -fd
            log_success "未跟踪文件已清理"
        fi
        
        return 0
    else
        log_error "恢复失败"
        return 1
    fi
}
