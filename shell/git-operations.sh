#!/bin/bash

# 🔄 Git操作模块 - 备份推送系统
# 版本：v2.0
# 功能：Git相关操作

# 加载依赖
source "$(dirname "${BASH_SOURCE[0]}")/config.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 检查Git仓库状态
check_git_repo() {
    local dir="${1:-$PWD}"
    
    if [ ! -d "$dir/.git" ]; then
        log_error "不是Git仓库: $dir"
        return 1
    fi
    
    if ! git -C "$dir" rev-parse --git-dir >/dev/null 2>&1; then
        log_error "Git仓库损坏: $dir"
        return 1
    fi
    
    log_debug "Git仓库检查通过: $dir"
    return 0
}

# 获取Git仓库信息
get_git_info() {
    local dir="${1:-$PWD}"
    
    if ! check_git_repo "$dir"; then
        return 1
    fi
    
    cd "$dir"
    
    local branch=$(git branch --show-current 2>/dev/null || echo "detached")
    local commit=$(git rev-parse --short HEAD 2>/dev/null || echo "no-commits")
    local remote_url=$(git remote get-url origin 2>/dev/null || echo "no-remote")
    local status=$(git status --porcelain 2>/dev/null | wc -l)
    
    echo "分支: $branch"
    echo "提交: $commit"
    echo "远程: $remote_url"
    echo "未提交文件: $status"
    
    cd - >/dev/null
}

# 清理Git历史中的大文件
clean_git_history() {
    local repo_dir="${1:-$PWD}"
    local patterns=("$@")
    
    if [ ${#patterns[@]} -eq 0 ]; then
        patterns=("${LFS_TRACK_PATTERNS[@]}")
    fi
    
    log_info "开始清理Git历史中的大文件..."
    
    cd "$repo_dir"
    
    # 备份当前状态
    local backup_branch="backup-$(date +%Y%m%d_%H%M%S)"
    git branch "$backup_branch"
    log_info "创建备份分支: $backup_branch"
    
    # 使用git filter-repo清理历史（如果可用）
    if command -v git-filter-repo >/dev/null 2>&1; then
        log_info "使用git-filter-repo清理历史..."
        
        for pattern in "${patterns[@]}"; do
            log_info "清理模式: $pattern"
            git filter-repo --path-glob "$pattern" --invert-paths --force
        done
    else
        # 使用BFG Repo-Cleaner（如果可用）
        if command -v bfg >/dev/null 2>&1; then
            log_info "使用BFG清理历史..."
            
            # 创建临时文件列表
            local temp_patterns="$TEMP_DIR/large_file_patterns.txt"
            ensure_directory "$TEMP_DIR"
            printf '%s\n' "${patterns[@]}" > "$temp_patterns"
            
            bfg --delete-files "$temp_patterns" --no-blob-protection .
        else
            log_warning "未找到git-filter-repo或bfg，跳过历史清理"
            log_warning "建议安装: pip install git-filter-repo"
            return 1
        fi
    fi
    
    # 强制垃圾回收
    log_info "执行垃圾回收..."
    git reflog expire --expire=now --all
    git gc --prune=now --aggressive
    
    log_success "Git历史清理完成"
    cd - >/dev/null
    return 0
}

# 初始化Git LFS
init_git_lfs() {
    local repo_dir="${1:-$PWD}"
    
    cd "$repo_dir"
    
    if ! check_command "git-lfs"; then
        log_error "Git LFS未安装，请先安装: git lfs install"
        return 1
    fi
    
    log_info "初始化Git LFS..."
    
    # 安装LFS hooks
    git lfs install
    
    # 配置跟踪模式
    for pattern in "${LFS_TRACK_PATTERNS[@]}"; do
        log_debug "添加LFS跟踪: $pattern"
        git lfs track "$pattern"
    done
    
    # 提交.gitattributes
    if [ -f ".gitattributes" ]; then
        git add .gitattributes
        if git diff --cached --quiet; then
            log_debug ".gitattributes无变化"
        else
            git commit -m "Configure Git LFS tracking patterns"
            log_success "Git LFS配置已提交"
        fi
    fi
    
    cd - >/dev/null
    return 0
}

# 检查大文件
check_large_files() {
    local repo_dir="${1:-$PWD}"
    local max_size_mb="${2:-100}"
    
    log_info "检查大文件 (>${max_size_mb}MB)..."
    
    cd "$repo_dir"
    
    # 查找大文件
    local large_files=()
    while IFS= read -r -d '' file; do
        local size=$(get_file_size "$file")
        local size_mb=$((size / 1024 / 1024))
        
        if [ $size_mb -gt $max_size_mb ]; then
            large_files+=("$file:${size_mb}MB")
            log_warning "发现大文件: $file (${size_mb}MB)"
        fi
    done < <(find . -type f -not -path './.git/*' -print0)
    
    if [ ${#large_files[@]} -eq 0 ]; then
        log_success "未发现大文件"
        return 0
    else
        log_warning "发现 ${#large_files[@]} 个大文件"
        
        # 检查是否已被LFS跟踪
        local untracked_large_files=()
        for file_info in "${large_files[@]}"; do
            local file="${file_info%:*}"
            if ! git lfs ls-files | grep -q "$file"; then
                untracked_large_files+=("$file_info")
            fi
        done
        
        if [ ${#untracked_large_files[@]} -gt 0 ]; then
            log_error "以下大文件未被LFS跟踪:"
            printf '%s\n' "${untracked_large_files[@]}"
            return 1
        else
            log_success "所有大文件都已被LFS跟踪"
            return 0
        fi
    fi
    
    cd - >/dev/null
}

# 智能添加文件
smart_git_add() {
    local repo_dir="${1:-$PWD}"
    local patterns=("${@:2}")
    
    if [ ${#patterns[@]} -eq 0 ]; then
        patterns=(".")
    fi
    
    cd "$repo_dir"
    
    log_info "智能添加文件到Git..."
    
    # 检查.gitignore
    if [ ! -f ".gitignore" ]; then
        log_warning ".gitignore文件不存在，将创建默认配置"
        create_default_gitignore
    fi
    
    # 分批添加文件
    local batch_size=$PUSH_BATCH_SIZE
    local file_count=0
    local batch_count=0
    
    for pattern in "${patterns[@]}"; do
        while IFS= read -r -d '' file; do
            # 跳过忽略的文件
            if should_ignore_file "$file"; then
                continue
            fi
            
            # 检查是否为大文件
            if is_large_file "$file"; then
                if [ "$LFS_ENABLED" = "true" ]; then
                    log_debug "大文件将通过LFS处理: $file"
                else
                    log_warning "跳过大文件: $file"
                    continue
                fi
            fi
            
            git add "$file"
            file_count=$((file_count + 1))
            
            # 分批处理
            if [ $((file_count % batch_size)) -eq 0 ]; then
                batch_count=$((batch_count + 1))
                log_info "已添加第 $batch_count 批文件 ($batch_size 个)"
            fi
            
        done < <(find "$pattern" -type f -print0 2>/dev/null)
    done
    
    log_success "共添加 $file_count 个文件"
    cd - >/dev/null
    return 0
}

# 检查文件是否应该被忽略
should_ignore_file() {
    local file="$1"
    
    for pattern in "${IGNORE_PATTERNS[@]}"; do
        if [[ "$file" == $pattern ]]; then
            return 0
        fi
    done
    
    return 1
}

# 创建默认.gitignore
create_default_gitignore() {
    local gitignore_content="# 子项目的 .git 目录 - 不跟踪子项目的Git历史
*/.git/

# 临时文件和日志
*/logs/
*/log/
*/tmp/
*/temp/
*/.tmp/

# Node.js 相关
*/node_modules/
*/npm-debug.log*
*/yarn-debug.log*
*/yarn-error.log*

# Go 相关
*/vendor/
*/*.exe
*/*.exe~
*/*.dll
*/*.so
*/*.dylib

# 编译产物
*/dist/
*/build/
*/target/
*/bin/
*/.next/

# IDE 文件
*/.vscode/
*/.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 备份和临时文件
*.backup
*.bak
*~
.backup/
.temp/

# 大文件（通过LFS管理）
# *.mp4
# *.db
# *.zip"

    echo "$gitignore_content" > .gitignore
    log_success "创建默认.gitignore文件"
}

# 提交更改
commit_changes() {
    local repo_dir="${1:-$PWD}"
    local message="$2"
    local auto_message="${3:-true}"
    
    cd "$repo_dir"
    
    # 检查是否有更改
    if git diff --cached --quiet; then
        log_info "没有需要提交的更改"
        return 0
    fi
    
    # 生成自动提交消息
    if [ "$auto_message" = "true" ] || [ -z "$message" ]; then
        local added_files=$(git diff --cached --name-only | wc -l)
        local modified_files=$(git diff --cached --name-status | grep -c '^M' || echo 0)
        local deleted_files=$(git diff --cached --name-status | grep -c '^D' || echo 0)
        local new_files=$(git diff --cached --name-status | grep -c '^A' || echo 0)
        
        message="Backup update: +${new_files} ~${modified_files} -${deleted_files} files

This is an automated backup commit containing:
- New files: $new_files
- Modified files: $modified_files  
- Deleted files: $deleted_files
- Total changes: $added_files

Generated at: $(date -Iseconds)
Backup system: v2.0"
    fi
    
    log_info "提交更改..."
    log_debug "提交消息: $message"
    
    if git commit -m "$message"; then
        log_success "提交成功"
        return 0
    else
        log_error "提交失败"
        return 1
    fi
    
    cd - >/dev/null
}

# 推送到远程仓库
push_to_remote() {
    local repo_dir="${1:-$PWD}"
    local remote="${2:-origin}"
    local branch="${3:-$(git branch --show-current)}"
    local force="${4:-$FORCE_PUSH}"
    
    cd "$repo_dir"
    
    log_info "推送到远程仓库: $remote/$branch"
    
    # 检查远程仓库连接
    if ! git ls-remote "$remote" >/dev/null 2>&1; then
        log_error "无法连接到远程仓库: $remote"
        return 1
    fi
    
    # 构建推送命令
    local push_cmd=("git" "push")
    
    if [ "$force" = "true" ]; then
        push_cmd+=("--force-with-lease")
        log_warning "使用强制推送模式"
    fi
    
    push_cmd+=("$remote" "$branch")
    
    # 执行推送
    log_info "执行推送命令: ${push_cmd[*]}"
    
    if retry_command $MAX_RETRIES 10 "${push_cmd[@]}"; then
        log_success "推送成功"
        
        # 推送LFS文件（如果启用）
        if [ "$LFS_ENABLED" = "true" ] && git lfs ls-files | grep -q .; then
            log_info "推送LFS文件..."
            if retry_command $MAX_RETRIES 30 git lfs push "$remote" "$branch"; then
                log_success "LFS文件推送成功"
            else
                log_error "LFS文件推送失败"
                return 1
            fi
        fi
        
        return 0
    else
        log_error "推送失败"
        return 1
    fi
    
    cd - >/dev/null
}

# 获取仓库统计信息
get_repo_stats() {
    local repo_dir="${1:-$PWD}"
    
    cd "$repo_dir"
    
    echo "=== Git仓库统计 ==="
    echo "总提交数: $(git rev-list --all --count)"
    echo "分支数: $(git branch -r | wc -l)"
    echo "标签数: $(git tag | wc -l)"
    echo "贡献者数: $(git shortlog -sn | wc -l)"
    echo "仓库大小: $(du -sh .git | cut -f1)"
    
    if [ "$LFS_ENABLED" = "true" ]; then
        echo "LFS文件数: $(git lfs ls-files | wc -l)"
        echo "LFS大小: $(git lfs ls-files -s | awk '{sum+=$2} END {print sum/1024/1024 "MB"}')"
    fi
    
    echo "最近提交: $(git log -1 --format='%h %s (%cr)')"
    
    cd - >/dev/null
}
