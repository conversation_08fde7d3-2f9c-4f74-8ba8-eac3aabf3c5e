#!/bin/bash

# 🔧 配置文件 - 备份推送系统
# 版本：v2.0
# 功能：统一配置管理

# 颜色定义
export RED='\033[0;31m'
export GREEN='\033[0;32m'
export YELLOW='\033[1;33m'
export BLUE='\033[0;34m'
export CYAN='\033[0;36m'
export WHITE='\033[1;37m'
export NC='\033[0m' # No Color

# 项目配置
export WORKSPACE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
export SHELL_DIR="$WORKSPACE_DIR/shell"
export BACKUP_DIR="$WORKSPACE_DIR/.backup"
export TEMP_DIR="$WORKSPACE_DIR/.temp"

# 子项目配置
declare -gA SUBPROJECTS=(
    ["gpt-load"]="https://github.com/CoolKingW/gpt-load.git"
    ["love"]="https://github.com/CoolKingW/love.git"
    ["new-api"]="https://github.com/CoolKingW/new-api.git"
)

# Git LFS 配置
export LFS_ENABLED=true
export LFS_TRACK_PATTERNS=(
    "*.mp4"
    "*.avi"
    "*.mov"
    "*.mkv"
    "*.webm"
    "*.db"
    "*.sqlite"
    "*.sqlite3"
    "*.zip"
    "*.tar.gz"
    "*.7z"
    "*.rar"
    "*.pdf"
    "*.psd"
    "*.ai"
    "*.exe"
    "*.dmg"
    "*.pkg"
    "*.deb"
    "*.rpm"
)

# 大文件处理配置
export MAX_FILE_SIZE="100M"  # 超过此大小的文件使用LFS
export CHUNK_SIZE="50M"      # 分块上传大小
export MAX_RETRIES=3         # 最大重试次数

# 忽略模式配置
export IGNORE_PATTERNS=(
    # 子项目Git目录
    "*/.git/*"
    
    # 临时文件
    "*/tmp/*"
    "*/temp/*"
    "*/.tmp/*"
    "*/logs/*"
    "*/log/*"
    
    # 编译产物
    "*/node_modules/*"
    "*/vendor/*"
    "*/dist/*"
    "*/build/*"
    "*/target/*"
    "*/.next/*"
    
    # IDE文件
    "*/.vscode/*"
    "*/.idea/*"
    "*.swp"
    "*.swo"
    "*~"
    
    # 系统文件
    ".DS_Store"
    "Thumbs.db"
    "desktop.ini"
    
    # 备份文件
    "*.backup"
    "*.bak"
    "*~"
)

# 备份配置
export BACKUP_RETENTION_DAYS=30
export BACKUP_COMPRESS=true
export BACKUP_ENCRYPT=false

# 推送配置
export PUSH_BATCH_SIZE=100   # 每批推送的文件数量
export PUSH_TIMEOUT=300      # 推送超时时间（秒）
export FORCE_PUSH=false      # 是否强制推送

# 日志配置
export LOG_LEVEL="INFO"      # DEBUG, INFO, WARN, ERROR
export LOG_FILE="$WORKSPACE_DIR/shell/backup.log"
export LOG_MAX_SIZE="10M"
export LOG_ROTATE_COUNT=5

# 通知配置
export NOTIFY_SUCCESS=true
export NOTIFY_ERROR=true
export NOTIFY_WEBHOOK=""     # 可选：Webhook URL用于通知

# 验证配置
export VERIFY_INTEGRITY=true
export VERIFY_CHECKSUM=true
export VERIFY_SIZE=true

# 性能配置
export PARALLEL_JOBS=4       # 并行处理任务数
export MEMORY_LIMIT="2G"     # 内存限制
export DISK_SPACE_THRESHOLD="1G"  # 最小磁盘空间要求

# 安全配置
export SECURE_MODE=true      # 安全模式
export BACKUP_PERMISSIONS=true  # 备份文件权限
export SANITIZE_FILENAMES=true  # 清理文件名

# 功能开关
export ENABLE_COMPRESSION=true
export ENABLE_DEDUPLICATION=true
export ENABLE_INCREMENTAL=true
export ENABLE_VERIFICATION=true
export ENABLE_ROLLBACK=true

# 导出所有配置变量
export SUBPROJECTS
export LFS_TRACK_PATTERNS
export IGNORE_PATTERNS
