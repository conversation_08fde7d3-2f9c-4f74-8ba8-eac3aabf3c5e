# 🏠 完整备份推送系统 v2.0

## 📋 系统概述

这是一个专为混合项目设计的完整备份推送系统，能够将包含多个子项目的工作空间作为完整备份推送到私有库，同时保持所有子项目的完全独立性。

## ✨ 核心特性

- **🔒 子项目独立性保护**：不影响任何子项目的Git仓库结构
- **💾 完整内容备份**：包含所有子项目的完整源代码
- **🚀 大文件支持**：自动通过Git LFS处理大文件上传
- **🧹 智能历史清理**：清理旧的跟踪记录，建立干净基线
- **📦 智能文件过滤**：自动排除不必要的文件和目录
- **🔧 模块化架构**：功能分离，易于维护和扩展

## 📁 文件结构

```
shell/
├── config.sh          # 系统配置文件
├── utils.sh           # 通用工具函数库
├── git-operations.sh  # Git操作模块
├── backup-manager.sh  # 备份管理模块
└── README.md          # 本文档
```

## 🚀 快速开始

### 1. 首次使用

```bash
# 分析项目状态
./git.sh analyze

# 执行完整备份
./git.sh backup
```

### 2. 日常使用

```bash
# 增量备份更新
./git.sh update

# 验证备份完整性
./git.sh verify
```

### 3. 交互式菜单

```bash
# 启动交互式菜单（推荐）
./git.sh menu
```

## 📚 命令参考

| 命令 | 功能 | 说明 |
|------|------|------|
| `analyze` | 分析项目状态 | 检查项目结构和备份可行性 |
| `backup` | 执行完整备份 | 清理历史并建立完整备份 |
| `update` | 增量备份更新 | 快速更新已有备份 |
| `verify` | 验证备份完整性 | 检查备份是否完整有效 |
| `clean` | 清理历史记录 | 清理旧的跟踪记录 |
| `info` | 系统状态查看 | 查看详细系统状态和统计 |
| `guide` | 使用指南 | 显示详细使用说明 |
| `menu` | 交互式菜单 | 进入交互式操作界面 |

## ⚙️ 配置说明

### 主要配置项 (config.sh)

```bash
# Git LFS 配置
LFS_ENABLED=true                    # 是否启用Git LFS
MAX_FILE_SIZE="100M"               # 大文件阈值

# 子项目配置
SUBPROJECTS=(
    ["gpt-load"]="https://github.com/CoolKingW/gpt-load.git"
    ["love"]="https://github.com/CoolKingW/love.git"
    ["new-api"]="https://github.com/CoolKingW/new-api.git"
)

# 忽略模式
IGNORE_PATTERNS=(
    "*/.git/*"          # 子项目Git目录
    "*/node_modules/*"  # Node.js依赖
    "*/logs/*"          # 日志文件
    # ... 更多模式
)
```

### LFS跟踪模式

系统自动跟踪以下类型的大文件：
- 视频文件：`*.mp4`, `*.avi`, `*.mov`, `*.mkv`, `*.webm`
- 数据库文件：`*.db`, `*.sqlite`, `*.sqlite3`
- 压缩文件：`*.zip`, `*.tar.gz`, `*.7z`, `*.rar`
- 其他大文件：`*.pdf`, `*.exe`, `*.dmg` 等

## 🔧 模块说明

### config.sh - 配置管理
- 系统全局配置
- 子项目定义
- LFS跟踪模式
- 忽略规则

### utils.sh - 工具函数库
- 日志系统
- 进度显示
- 文件操作
- 错误处理

### git-operations.sh - Git操作
- Git仓库检查
- 大文件处理
- LFS初始化
- 智能文件添加

### backup-manager.sh - 备份管理
- 项目状态分析
- 历史清理
- 备份基线建立
- 完整性验证

## 🛡️ 安全特性

- **备份保护**：操作前自动创建备份分支
- **回滚支持**：支持恢复到任意历史状态
- **完整性验证**：多层次的数据完整性检查
- **权限保护**：保持文件权限和属性

## 📊 与传统方案对比

| 特性 | 传统Git Submodules | 本系统 |
|------|-------------------|--------|
| 备份完整性 | ❌ 只有引用 | ✅ 完整代码 |
| 子项目独立性 | ⚠️ 需要修改结构 | ✅ 完全独立 |
| 大文件支持 | ❌ 需要额外配置 | ✅ 自动处理 |
| 使用复杂度 | ⚠️ 较复杂 | ✅ 简单易用 |
| 团队协作 | ⚠️ 需要培训 | ✅ 透明操作 |

## 🔍 故障排除

### 常见问题

1. **Git LFS未安装**
   ```bash
   # 安装Git LFS
   git lfs install
   ```

2. **权限问题**
   ```bash
   # 确保脚本可执行
   chmod +x git.sh
   ```

3. **模块加载失败**
   ```bash
   # 检查shell目录是否存在
   ls -la shell/
   ```

### 日志查看

```bash
# 查看系统日志
tail -f shell/backup.log

# 查看详细调试信息
LOG_LEVEL=DEBUG ./git.sh analyze
```

## 📈 性能优化

- **并行处理**：支持多任务并行执行
- **增量备份**：只处理变更的文件
- **智能缓存**：避免重复操作
- **分批上传**：大量文件分批处理

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 📞 支持

如有问题或建议，请：
1. 查看使用指南：`./git.sh guide`
2. 检查日志文件：`shell/backup.log`
3. 提交 Issue 或 Pull Request
