#!/bin/bash

# 🛠️ 工具函数库 - 备份推送系统
# 版本：v2.0
# 功能：通用工具函数

# 加载配置
source "$(dirname "${BASH_SOURCE[0]}")/config.sh"

# 日志函数
log_debug() {
    [ "$LOG_LEVEL" = "DEBUG" ] && echo -e "${BLUE}[DEBUG $(date '+%H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}[INFO $(date '+%H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS $(date '+%H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING $(date '+%H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR $(date '+%H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

# 进度条函数
show_progress() {
    local current=$1
    local total=$2
    local message="${3:-Processing}"
    local width=50
    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    printf "\r${CYAN}%s${NC} [" "$message"
    printf "%*s" $filled | tr ' ' '='
    printf "%*s" $empty | tr ' ' '-'
    printf "] %d%% (%d/%d)" $percentage $current $total
    
    if [ $current -eq $total ]; then
        echo
    fi
}

# 等待用户确认
confirm_action() {
    local message="$1"
    local default="${2:-N}"
    local response
    
    while true; do
        if [ "$default" = "Y" ]; then
            echo -n -e "${YELLOW}$message [Y/n]: ${NC}"
        else
            echo -n -e "${YELLOW}$message [y/N]: ${NC}"
        fi
        
        read -r response
        
        if [ -z "$response" ]; then
            response="$default"
        fi
        
        case "$response" in
            [Yy]|[Yy][Ee][Ss])
                return 0
                ;;
            [Nn]|[Nn][Oo])
                return 1
                ;;
            *)
                echo -e "${RED}请输入 y/yes 或 n/no${NC}"
                ;;
        esac
    done
}

# 检查命令是否存在
check_command() {
    local cmd="$1"
    if ! command -v "$cmd" >/dev/null 2>&1; then
        log_error "命令不存在: $cmd"
        return 1
    fi
    return 0
}

# 检查磁盘空间
check_disk_space() {
    local path="${1:-$WORKSPACE_DIR}"
    local required="${2:-$DISK_SPACE_THRESHOLD}"
    
    local available=$(df -h "$path" | awk 'NR==2 {print $4}' | sed 's/[^0-9.]//g')
    local required_num=$(echo "$required" | sed 's/[^0-9.]//g')
    
    if (( $(echo "$available < $required_num" | bc -l) )); then
        log_error "磁盘空间不足: 可用 ${available}G, 需要 ${required}"
        return 1
    fi
    
    log_debug "磁盘空间检查通过: 可用 ${available}G"
    return 0
}

# 获取文件大小
get_file_size() {
    local file="$1"
    if [ -f "$file" ]; then
        stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo "0"
    else
        echo "0"
    fi
}

# 格式化文件大小
format_size() {
    local size=$1
    local units=("B" "KB" "MB" "GB" "TB")
    local unit=0
    
    while [ $size -gt 1024 ] && [ $unit -lt 4 ]; do
        size=$((size / 1024))
        unit=$((unit + 1))
    done
    
    echo "${size}${units[$unit]}"
}

# 检查文件是否为大文件
is_large_file() {
    local file="$1"
    local max_size_bytes=$(echo "$MAX_FILE_SIZE" | sed 's/M/*1024*1024/g; s/G/*1024*1024*1024/g' | bc)
    local file_size=$(get_file_size "$file")
    
    [ "$file_size" -gt "$max_size_bytes" ]
}

# 创建目录
ensure_directory() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        log_debug "创建目录: $dir"
    fi
}

# 清理临时文件
cleanup_temp() {
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
        log_debug "清理临时目录: $TEMP_DIR"
    fi
}

# 备份文件
backup_file() {
    local source="$1"
    local backup_name="${2:-$(basename "$source").backup.$(date +%Y%m%d_%H%M%S)}"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    ensure_directory "$BACKUP_DIR"
    
    if cp "$source" "$backup_path"; then
        log_debug "备份文件: $source -> $backup_path"
        echo "$backup_path"
        return 0
    else
        log_error "备份失败: $source"
        return 1
    fi
}

# 恢复文件
restore_file() {
    local backup_path="$1"
    local target="$2"
    
    if [ -f "$backup_path" ]; then
        if cp "$backup_path" "$target"; then
            log_success "恢复文件: $backup_path -> $target"
            return 0
        else
            log_error "恢复失败: $backup_path -> $target"
            return 1
        fi
    else
        log_error "备份文件不存在: $backup_path"
        return 1
    fi
}

# 计算文件校验和
calculate_checksum() {
    local file="$1"
    local algorithm="${2:-sha256}"
    
    case "$algorithm" in
        md5)
            md5sum "$file" 2>/dev/null | cut -d' ' -f1
            ;;
        sha1)
            sha1sum "$file" 2>/dev/null | cut -d' ' -f1
            ;;
        sha256)
            sha256sum "$file" 2>/dev/null | cut -d' ' -f1
            ;;
        *)
            log_error "不支持的校验算法: $algorithm"
            return 1
            ;;
    esac
}

# 验证文件完整性
verify_file_integrity() {
    local file="$1"
    local expected_checksum="$2"
    local algorithm="${3:-sha256}"
    
    local actual_checksum=$(calculate_checksum "$file" "$algorithm")
    
    if [ "$actual_checksum" = "$expected_checksum" ]; then
        log_debug "文件完整性验证通过: $file"
        return 0
    else
        log_error "文件完整性验证失败: $file"
        log_error "期望: $expected_checksum"
        log_error "实际: $actual_checksum"
        return 1
    fi
}

# 重试执行函数
retry_command() {
    local max_attempts="$1"
    local delay="$2"
    shift 2
    local command=("$@")
    
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        log_debug "尝试执行 (第 $attempt 次): ${command[*]}"
        
        if "${command[@]}"; then
            log_debug "命令执行成功"
            return 0
        else
            log_warning "命令执行失败 (第 $attempt 次)"
            if [ $attempt -lt $max_attempts ]; then
                log_info "等待 ${delay}s 后重试..."
                sleep "$delay"
            fi
        fi
        
        attempt=$((attempt + 1))
    done
    
    log_error "命令执行失败，已达到最大重试次数: $max_attempts"
    return 1
}

# 发送通知
send_notification() {
    local type="$1"  # success, error, info, warning
    local message="$2"
    local title="${3:-备份推送系统}"
    
    # 控制台通知
    case "$type" in
        success)
            [ "$NOTIFY_SUCCESS" = "true" ] && log_success "$message"
            ;;
        error)
            [ "$NOTIFY_ERROR" = "true" ] && log_error "$message"
            ;;
        info)
            log_info "$message"
            ;;
        warning)
            log_warning "$message"
            ;;
    esac
    
    # Webhook通知（如果配置了）
    if [ -n "$NOTIFY_WEBHOOK" ]; then
        local payload="{\"title\":\"$title\",\"type\":\"$type\",\"message\":\"$message\",\"timestamp\":\"$(date -Iseconds)\"}"
        curl -s -X POST -H "Content-Type: application/json" -d "$payload" "$NOTIFY_WEBHOOK" >/dev/null 2>&1 || true
    fi
}

# 清理旧备份
cleanup_old_backups() {
    local backup_dir="${1:-$BACKUP_DIR}"
    local retention_days="${2:-$BACKUP_RETENTION_DAYS}"
    
    if [ -d "$backup_dir" ]; then
        log_info "清理 $retention_days 天前的备份文件..."
        find "$backup_dir" -type f -mtime +$retention_days -delete
        log_success "旧备份清理完成"
    fi
}

# 初始化日志
init_logging() {
    ensure_directory "$(dirname "$LOG_FILE")"
    
    # 日志轮转
    if [ -f "$LOG_FILE" ]; then
        local log_size=$(get_file_size "$LOG_FILE")
        local max_size_bytes=$(echo "$LOG_MAX_SIZE" | sed 's/M/*1024*1024/g; s/G/*1024*1024*1024/g' | bc)
        
        if [ "$log_size" -gt "$max_size_bytes" ]; then
            for i in $(seq $((LOG_ROTATE_COUNT - 1)) -1 1); do
                [ -f "${LOG_FILE}.$i" ] && mv "${LOG_FILE}.$i" "${LOG_FILE}.$((i + 1))"
            done
            mv "$LOG_FILE" "${LOG_FILE}.1"
        fi
    fi
    
    # 记录启动信息
    echo "=== 备份推送系统启动 $(date) ===" >> "$LOG_FILE"
}

# 陷阱处理 - 清理函数
trap_cleanup() {
    log_info "正在清理临时文件..."
    cleanup_temp
    log_info "清理完成"
}

# 设置陷阱
trap trap_cleanup EXIT INT TERM
