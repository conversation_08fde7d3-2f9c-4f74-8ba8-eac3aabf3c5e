{"root": ["../../src/main.ts", "../../src/vite-env.d.ts", "../../src/api/dashboard.ts", "../../src/api/keys.ts", "../../src/api/logs.ts", "../../src/api/settings.ts", "../../src/router/index.ts", "../../src/services/auth.ts", "../../src/services/version.ts", "../../src/types/env.d.ts", "../../src/types/models.ts", "../../src/utils/app-state.ts", "../../src/utils/clipboard.ts", "../../src/utils/display.ts", "../../src/utils/http.ts", "../../src/utils/state.ts", "../../src/App.vue", "../../src/components/AppFooter.vue", "../../src/components/BaseInfoCard.vue", "../../src/components/GlobalProviders.vue", "../../src/components/GlobalTaskProgressBar.vue", "../../src/components/Layout.vue", "../../src/components/LineChart.vue", "../../src/components/Logout.vue", "../../src/components/NavBar.vue", "../../src/components/common/ProxyKeysInput.vue", "../../src/components/keys/GroupFormModal.vue", "../../src/components/keys/GroupInfoCard.vue", "../../src/components/keys/GroupList.vue", "../../src/components/keys/KeyCreateDialog.vue", "../../src/components/keys/KeyDeleteDialog.vue", "../../src/components/keys/KeyTable.vue", "../../src/components/logs/LogTable.vue", "../../src/views/Dashboard.vue", "../../src/views/Keys.vue", "../../src/views/Login.vue", "../../src/views/Logs.vue", "../../src/views/Settings.vue"], "version": "5.8.3"}