{"version": 3, "names": ["_toIdentifier", "require", "toBindingIdentifierName", "name", "toIdentifier"], "sources": ["../../src/converters/toBindingIdentifierName.ts"], "sourcesContent": ["import toIdentifier from \"./toIdentifier.ts\";\n\nexport default function toBindingIdentifierName(name: string): string {\n  name = toIdentifier(name);\n  if (name === \"eval\" || name === \"arguments\") name = \"_\" + name;\n\n  return name;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,OAAA;AAEe,SAASC,uBAAuBA,CAACC,IAAY,EAAU;EACpEA,IAAI,GAAG,IAAAC,qBAAY,EAACD,IAAI,CAAC;EACzB,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,WAAW,EAAEA,IAAI,GAAG,GAAG,GAAGA,IAAI;EAE9D,OAAOA,IAAI;AACb", "ignoreList": []}