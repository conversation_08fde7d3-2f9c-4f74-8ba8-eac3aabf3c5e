import{d as j,c as C,g as n,p as v,A as Ke,r as b,B as je,q as he,s as le,i as V,w as t,u as e,C as Ae,a as l,D as Ce,E as P,G as X,k as i,m as M,H as N,v as $e,I as xe,x as fe,j as ne,J as O,t as w,F as ge,K as Z,f as ke,L as B,_ as ue,M as Me,o as De,z as Te,b as Ne,e as W,O as Fe,P as He,Q as Je,l as Ue,R as J,S as Pe,y as Re,N as Ye,T as We,U as Qe,V as ce,W as Xe,X as Ze}from"./index-C-we6bTS.js";import{P as et,s as tt,c as Ie}from"./ProxyKeysInput-B_zpt-pa.js";import{m as lt,g as we,a as Ge}from"./display-DuIJKIJ5.js";import{E as Ee,a as qe,S as <PERSON>}from"./Search-LBeTRPTK.js";const ot={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ze=j({name:"Add",render:function(d,s){return v(),C("svg",ot,s[0]||(s[0]=[n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 112v288"},null,-1),n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M400 256H112"},null,-1)]))}}),nt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},st=j({name:"AddCircleOutline",render:function(d,s){return v(),C("svg",nt,s[0]||(s[0]=[n("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 176v160"},null,-1),n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M336 256H176"},null,-1)]))}}),at={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},rt=j({name:"AlertCircleOutline",render:function(d,s){return v(),C("svg",at,s[0]||(s[0]=[n("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),n("path",{d:"M250.26 166.05L256 288l5.73-121.95a5.74 5.74 0 0 0-5.79-6h0a5.74 5.74 0 0 0-5.68 6z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),n("path",{d:"M256 367.91a20 20 0 1 1 20-20a20 20 0 0 1-20 20z",fill:"currentColor"},null,-1)]))}}),it={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ut=j({name:"CheckmarkCircle",render:function(d,s){return v(),C("svg",it,s[0]||(s[0]=[n("path",{d:"M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208s208-93.31 208-208S370.69 48 256 48zm108.25 138.29l-134.4 160a16 16 0 0 1-12 5.71h-.27a16 16 0 0 1-11.89-5.3l-57.6-64a16 16 0 1 1 23.78-21.4l45.29 50.32l122.59-145.91a16 16 0 0 1 24.5 20.58z",fill:"currentColor"},null,-1)]))}}),dt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Se=j({name:"Close",render:function(d,s){return v(),C("svg",dt,s[0]||(s[0]=[n("path",{d:"M289.94 256l95-95A24 24 0 0 0 351 127l-95 95l-95-95a24 24 0 0 0-34 34l95 95l-95 95a24 24 0 1 0 34 34l95-95l95 95a24 24 0 0 0 34-34z",fill:"currentColor"},null,-1)]))}}),pt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Be=j({name:"CopyOutline",render:function(d,s){return v(),C("svg",pt,s[0]||(s[0]=[n("rect",{x:"128",y:"128",width:"336",height:"336",rx:"57",ry:"57",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),n("path",{d:"M383.5 128l.5-24a56.16 56.16 0 0 0-56-56H112a64.19 64.19 0 0 0-64 64v216a56.16 56.16 0 0 0 56 56h24",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1)]))}}),ct={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Q=j({name:"HelpCircleOutline",render:function(d,s){return v(),C("svg",ct,s[0]||(s[0]=[n("path",{d:"M256 80a176 176 0 1 0 176 176A176 176 0 0 0 256 80z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),n("path",{d:"M200 202.29s.84-17.5 19.57-32.57C230.68 160.77 244 158.18 256 158c10.93-.14 20.69 1.67 26.53 4.45c10 4.76 29.47 16.38 29.47 41.09c0 26-17 37.81-36.37 50.8S251 281.43 251 296",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"28"},null,-1),n("circle",{cx:"250",cy:"348",r:"20",fill:"currentColor"},null,-1)]))}}),ft={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},gt=j({name:"Pencil",render:function(d,s){return v(),C("svg",ft,s[0]||(s[0]=[n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"44",d:"M358.62 129.28L86.49 402.08L70 442l39.92-16.49l272.8-272.13l-24.1-24.1z"},null,-1),n("path",{d:"M413.07 74.84l-11.79 11.78l24.1 24.1l11.79-11.79a16.51 16.51 0 0 0 0-23.34l-.75-.75a16.51 16.51 0 0 0-23.35 0z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"44"},null,-1)]))}}),vt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Oe=j({name:"Remove",render:function(d,s){return v(),C("svg",vt,s[0]||(s[0]=[n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M400 256H112"},null,-1)]))}}),mt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},_t=j({name:"RemoveCircleOutline",render:function(d,s){return v(),C("svg",mt,s[0]||(s[0]=[n("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),n("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M336 256H176"},null,-1)]))}}),yt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ht=j({name:"Trash",render:function(d,s){return v(),C("svg",yt,s[0]||(s[0]=[n("path",{d:"M296 64h-80a7.91 7.91 0 0 0-8 8v24h96V72a7.91 7.91 0 0 0-8-8z",fill:"none"},null,-1),n("path",{d:"M432 96h-96V72a40 40 0 0 0-40-40h-80a40 40 0 0 0-40 40v24H80a16 16 0 0 0 0 32h17l19 304.92c1.42 26.85 22 47.08 48 47.08h184c26.13 0 46.3-19.78 48-47l19-305h17a16 16 0 0 0 0-32zM192.57 416H192a16 16 0 0 1-16-15.43l-8-224a16 16 0 1 1 32-1.14l8 224A16 16 0 0 1 192.57 416zM272 400a16 16 0 0 1-32 0V176a16 16 0 0 1 32 0zm32-304h-96V72a7.91 7.91 0 0 1 8-8h80a7.91 7.91 0 0 1 8 8zm32 304.57A16 16 0 0 1 320 416h-.58A16 16 0 0 1 304 399.43l8-224a16 16 0 1 1 32 1.14z",fill:"currentColor"},null,-1)]))}}),wt={class:"form-section"},kt={class:"form-row"},bt={class:"form-label-with-tooltip"},Ct={class:"form-label-with-tooltip"},$t={class:"form-row"},xt={class:"form-label-with-tooltip"},Gt={class:"form-label-with-tooltip"},It={class:"form-row"},zt={class:"form-label-with-tooltip"},At={class:"form-label-with-tooltip"},Tt={key:1,class:"form-item-half"},St={class:"form-label-with-tooltip"},Nt={class:"form-label-with-tooltip"},Ot={class:"form-section",style:{"margin-top":"10px"}},Kt={class:"form-label-with-tooltip"},Mt={class:"upstream-row"},Dt={class:"upstream-url"},Ut={class:"upstream-weight"},Pt={class:"upstream-actions"},Rt={class:"form-section",style:{"margin-top":"10px"}},Et={class:"config-section"},qt={class:"config-title-with-tooltip"},Lt={class:"config-items"},Bt={class:"form-label-with-tooltip"},Vt={class:"config-item-content"},jt={class:"config-select"},Ft={class:"config-value"},Ht={class:"config-actions"},Jt={style:{"margin-top":"12px","padding-left":"120px"}},Yt={class:"config-section"},Wt={class:"form-label-with-tooltip"},Qt={style:{display:"flex","justify-content":"flex-end",gap:"12px"}},Xt=j({__name:"GroupFormModal",props:{show:{type:Boolean},group:{default:null}},emits:["update:show","success","switchToGroup"],setup(D,{emit:d}){const s=D,I=d,y=Ke(),h=b(!1),A=b(),r=je({name:"",display_name:"",description:"",upstreams:[{url:"",weight:1}],channel_type:"openai",sort:1,test_model:"",validation_endpoint:"",param_overrides:"",config:{},configItems:[],proxy_keys:""}),R=b([]),U=b([]),T=b(!1),g=b(!1),_=b({test_model:!1,upstream:!1}),x=he(()=>{switch(r.channel_type){case"openai":return"gpt-4.1-nano";case"gemini":return"gemini-2.0-flash-lite";case"anthropic":return"claude-3-haiku-20240307";default:return"请输入模型名称"}}),$=he(()=>{switch(r.channel_type){case"openai":return"https://api.openai.com";case"gemini":return"https://generativelanguage.googleapis.com";case"anthropic":return"https://api.anthropic.com";default:return"请输入上游地址"}}),Y=he(()=>{switch(r.channel_type){case"openai":return"/v1/chat/completions";case"anthropic":return"/v1/messages";case"gemini":return"";default:return"请输入验证端点路径"}}),oe={name:[{required:!0,message:"请输入分组名称",trigger:["blur","input"]},{pattern:/^[a-z0-9_-]{3,30}$/,message:"只能包含小写字母、数字、中划线或下划线，长度3-30位",trigger:["blur","input"]}],channel_type:[{required:!0,message:"请选择渠道类型",trigger:["blur","change"]}],test_model:[{required:!0,message:"请输入测试模型",trigger:["blur","input"]}],upstreams:[{type:"array",min:1,message:"至少需要一个上游地址",trigger:["blur","change"]}]};le(()=>s.show,k=>{k&&(T.value||te(),g.value||_e(),ve(),s.group&&F())}),le(()=>r.channel_type,(k,o)=>{!s.group&&o&&((!_.value.test_model||r.test_model===ae(o))&&(r.test_model=x.value,_.value.test_model=!1),r.upstreams.length>0&&(!_.value.upstream||r.upstreams[0].url===de(o))&&(r.upstreams[0].url=$.value,_.value.upstream=!1))});function ae(k){switch(k){case"openai":return"gpt-4.1-nano";case"gemini":return"gemini-2.0-flash-lite";case"anthropic":return"claude-3-haiku-20240307";default:return""}}function de(k){switch(k){case"openai":return"https://api.openai.com";case"gemini":return"https://generativelanguage.googleapis.com";case"anthropic":return"https://api.anthropic.com";default:return""}}function ve(){const k=!s.group,o="openai";r.channel_type=o,Object.assign(r,{name:"",display_name:"",description:"",upstreams:[{url:k?$.value:"",weight:1}],channel_type:o,sort:1,test_model:k?x.value:"",validation_endpoint:"",param_overrides:"",config:{},configItems:[],proxy_keys:""}),k&&(_.value={test_model:!1,upstream:!1})}function F(){var o;if(!s.group)return;const k=Object.entries(s.group.config||{}).map(([S,m])=>({key:S,value:Number(m)||0}));Object.assign(r,{name:s.group.name||"",display_name:s.group.display_name||"",description:s.group.description||"",upstreams:(o=s.group.upstreams)!=null&&o.length?[...s.group.upstreams]:[{url:"",weight:1}],channel_type:s.group.channel_type||"openai",sort:s.group.sort||1,test_model:s.group.test_model||"",validation_endpoint:s.group.validation_endpoint||"",param_overrides:JSON.stringify(s.group.param_overrides||{},null,2),config:{},configItems:k,proxy_keys:s.group.proxy_keys||""})}async function te(){const k=await tt.getChannelTypes()||[];R.value=(k==null?void 0:k.map(o=>({label:o,value:o})))||[],T.value=!0}function re(){r.upstreams.push({url:"",weight:1})}function me(k){r.upstreams.length>1?r.upstreams.splice(k,1):y.warning("至少需要保留一个上游地址")}async function _e(){const k=await B.getGroupConfigOptions();U.value=k||[],g.value=!0}function p(){r.configItems.push({key:"",value:0})}function f(k){r.configItems.splice(k,1)}function L(k,o){const S=U.value.find(m=>m.key===o);S&&(r.configItems[k].value=S.default_value||0)}function H(){I("update:show",!1)}async function se(){var k,o,S;if(!h.value)try{await((k=A.value)==null?void 0:k.validate()),h.value=!0;let m={};if(r.param_overrides)try{m=JSON.parse(r.param_overrides)}catch{y.error("参数覆盖必须是有效的 JSON 格式");return}const a={};r.configItems.forEach(ee=>{ee.key&&ee.key.trim()&&(a[ee.key]=ee.value)});const K={name:r.name,display_name:r.display_name,description:r.description,upstreams:r.upstreams.filter(ee=>ee.url.trim()),channel_type:r.channel_type,sort:r.sort,test_model:r.test_model,validation_endpoint:r.validation_endpoint,param_overrides:m,config:a,proxy_keys:r.proxy_keys};let z;(o=s.group)!=null&&o.id?z=await B.updateGroup(s.group.id,K):z=await B.createGroup(K),I("success",z),!((S=s.group)!=null&&S.id)&&z.id&&I("switchToGroup",z.id),H()}finally{h.value=!1}}return(k,o)=>{const S=Z("n-collapse-item"),m=Z("n-collapse");return v(),V(e(Ae),{show:k.show,"onUpdate:show":H,class:"group-form-modal"},{default:t(()=>[l(e(ke),{style:{width:"800px"},title:k.group?"编辑分组":"创建分组",bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{"header-extra":t(()=>[l(e(O),{quaternary:"",circle:"",onClick:H},{icon:t(()=>[l(e(N),{component:e(Se)},null,8,["component"])]),_:1})]),footer:t(()=>[n("div",Qt,[l(e(O),{onClick:H},{default:t(()=>o[39]||(o[39]=[i("取消")])),_:1,__:[39]}),l(e(O),{type:"primary",onClick:se,loading:h.value},{default:t(()=>[i(w(k.group?"更新":"创建"),1)]),_:1},8,["loading"])])]),default:t(()=>[l(e(Ce),{ref_key:"formRef",ref:A,model:r,rules:oe,"label-placement":"left","label-width":"120px","require-mark-placement":"right-hanging"},{default:t(()=>[n("div",wt,[o[26]||(o[26]=n("h4",{class:"section-title"},"基础信息",-1)),n("div",kt,[l(e(P),{label:"分组名称",path:"name",class:"form-item-half"},{label:t(()=>[n("div",bt,[o[11]||(o[11]=i(" 分组名称 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[10]||(o[10]=i(" 作为API路由的一部分，只能包含小写字母、数字、中划线或下划线，长度3-30位。例如：gemini、openai-2 "))]),_:1,__:[10]})])]),default:t(()=>[l(e(X),{value:r.name,"onUpdate:value":o[0]||(o[0]=a=>r.name=a),placeholder:"gemini"},null,8,["value"])]),_:1}),l(e(P),{label:"显示名称",path:"display_name",class:"form-item-half"},{label:t(()=>[n("div",Ct,[o[13]||(o[13]=i(" 显示名称 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[12]||(o[12]=i(" 用于在界面上显示的友好名称，可以包含中文和特殊字符。如果不填写，将使用分组名称作为显示名称 "))]),_:1,__:[12]})])]),default:t(()=>[l(e(X),{value:r.display_name,"onUpdate:value":o[1]||(o[1]=a=>r.display_name=a),placeholder:"Google Gemini"},null,8,["value"])]),_:1})]),n("div",$t,[l(e(P),{label:"渠道类型",path:"channel_type",class:"form-item-half"},{label:t(()=>[n("div",xt,[o[15]||(o[15]=i(" 渠道类型 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[14]||(o[14]=i(" 选择API提供商类型，决定了请求格式和认证方式。支持OpenAI、Gemini、Anthropic等主流AI服务商 "))]),_:1,__:[14]})])]),default:t(()=>[l(e($e),{value:r.channel_type,"onUpdate:value":o[2]||(o[2]=a=>r.channel_type=a),options:R.value,placeholder:"请选择渠道类型"},null,8,["value","options"])]),_:1}),l(e(P),{label:"排序",path:"sort",class:"form-item-half"},{label:t(()=>[n("div",Gt,[o[17]||(o[17]=i(" 排序 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[16]||(o[16]=i(" 决定分组在列表中的显示顺序，数字越小越靠前。建议使用10、20、30这样的间隔数字，便于后续调整 "))]),_:1,__:[16]})])]),default:t(()=>[l(e(xe),{value:r.sort,"onUpdate:value":o[3]||(o[3]=a=>r.sort=a),min:0,placeholder:"排序值",style:{width:"100%"}},null,8,["value"])]),_:1})]),n("div",It,[l(e(P),{label:"测试模型",path:"test_model",class:"form-item-half"},{label:t(()=>[n("div",zt,[o[19]||(o[19]=i(" 测试模型 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[18]||(o[18]=i(" 用于验证API密钥有效性的模型名称。系统会使用这个模型发送测试请求来检查密钥是否可用，请尽量使用轻量快速的模型 "))]),_:1,__:[18]})])]),default:t(()=>[l(e(X),{value:r.test_model,"onUpdate:value":o[4]||(o[4]=a=>r.test_model=a),placeholder:x.value,onInput:o[5]||(o[5]=()=>!s.group&&(_.value.test_model=!0))},null,8,["value","placeholder"])]),_:1}),r.channel_type!=="gemini"?(v(),V(e(P),{key:0,label:"测试路径",path:"validation_endpoint",class:"form-item-half"},{label:t(()=>[n("div",At,[o[21]||(o[21]=i(" 测试路径 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[20]||(o[20]=n("div",null,[i(" 自定义用于验证密钥的API端点路径。如果不填写，将使用默认路径： "),n("br"),i(" • OpenAI: /v1/chat/completions "),n("br"),i(" • Anthropic: /v1/messages "),n("br"),i(" 如需使用非标准路径，请在此填写完整的API路径 ")],-1))]),_:1,__:[20]})])]),default:t(()=>[l(e(X),{value:r.validation_endpoint,"onUpdate:value":o[6]||(o[6]=a=>r.validation_endpoint=a),placeholder:Y.value||"可选，自定义用于验证key的API路径"},null,8,["value","placeholder"])]),_:1})):(v(),C("div",Tt))]),l(e(P),{label:"代理密钥",path:"proxy_keys"},{label:t(()=>[n("div",St,[o[23]||(o[23]=i(" 代理密钥 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[22]||(o[22]=i(" 分组专用代理密钥，用于访问此分组的代理端点。多个密钥请用逗号分隔。 "))]),_:1,__:[22]})])]),default:t(()=>[l(et,{modelValue:r.proxy_keys,"onUpdate:modelValue":o[7]||(o[7]=a=>r.proxy_keys=a),placeholder:"多个密钥请用英文逗号 , 分隔",size:"medium"},null,8,["modelValue"])]),_:1}),l(e(P),{label:"描述",path:"description"},{label:t(()=>[n("div",Nt,[o[25]||(o[25]=i(" 描述 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[24]||(o[24]=i(" 分组的详细说明，帮助团队成员了解该分组的用途和特点。支持多行文本 "))]),_:1,__:[24]})])]),default:t(()=>[l(e(X),{value:r.description,"onUpdate:value":o[8]||(o[8]=a=>r.description=a),type:"textarea",placeholder:"",rows:1,autosize:{minRows:1,maxRows:5},style:{resize:"none"}},null,8,["value"])]),_:1})]),n("div",Ot,[o[31]||(o[31]=n("h4",{class:"section-title"},"上游地址",-1)),(v(!0),C(ge,null,fe(r.upstreams,(a,K)=>(v(),V(e(P),{key:K,label:`上游 ${K+1}`,path:`upstreams[${K}].url`,rule:{required:!0,message:"",trigger:["blur","input"]}},{label:t(()=>[n("div",Kt,[i(" 上游 "+w(K+1)+" ",1),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[27]||(o[27]=i(" API服务器的完整URL地址。多个上游可以实现负载均衡和故障转移，提高服务可用性 "))]),_:1,__:[27]})])]),default:t(()=>[n("div",Mt,[n("div",Dt,[l(e(X),{value:a.url,"onUpdate:value":z=>a.url=z,placeholder:$.value,onInput:()=>!s.group&&K===0&&(_.value.upstream=!0)},null,8,["value","onUpdate:value","placeholder","onInput"])]),n("div",Ut,[o[29]||(o[29]=n("span",{class:"weight-label"},"权重",-1)),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(xe),{value:a.weight,"onUpdate:value":z=>a.weight=z,min:1,placeholder:"权重"},null,8,["value","onUpdate:value"])]),default:t(()=>[o[28]||(o[28]=i(" 负载均衡权重，数值越大被选中的概率越高。例如：权重为2的上游被选中的概率是权重为1的两倍 "))]),_:2,__:[28]},1024)]),n("div",Pt,[r.upstreams.length>1?(v(),V(e(O),{key:0,onClick:z=>me(K),type:"error",quaternary:"",circle:"",size:"small"},{icon:t(()=>[l(e(N),{component:e(Oe)},null,8,["component"])]),_:2},1032,["onClick"])):ne("",!0)])])]),_:2},1032,["label","path"]))),128)),l(e(P),null,{default:t(()=>[l(e(O),{onClick:re,dashed:"",style:{width:"100%"}},{icon:t(()=>[l(e(N),{component:e(ze)},null,8,["component"])]),default:t(()=>[o[30]||(o[30]=i(" 添加上游地址 "))]),_:1,__:[30]})]),_:1})]),n("div",Rt,[l(m,null,{default:t(()=>[l(S,{name:"advanced"},{header:t(()=>o[32]||(o[32]=[i("高级配置")])),default:t(()=>[n("div",Et,[n("h5",qt,[o[34]||(o[34]=i(" 分组配置 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon config-help"},null,8,["component"])]),default:t(()=>[o[33]||(o[33]=i(" 针对此分组的专用配置参数，如超时时间、重试次数等。这些配置会覆盖全局默认设置 "))]),_:1,__:[33]})]),n("div",Lt,[(v(!0),C(ge,null,fe(r.configItems,(a,K)=>(v(),V(e(P),{key:K,class:"config-item-row",label:`配置 ${K+1}`,path:`configItems[${K}].key`,rule:{required:!0,message:"",trigger:["blur","change"]}},{label:t(()=>[n("div",Bt,[i(" 配置 "+w(K+1)+" ",1),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon"},null,8,["component"])]),default:t(()=>[o[35]||(o[35]=i(" 选择要配置的参数类型，然后设置对应的数值。不同参数有不同的作用和取值范围 "))]),_:1,__:[35]})])]),default:t(()=>[n("div",Vt,[n("div",jt,[l(e($e),{value:a.key,"onUpdate:value":[z=>a.key=z,z=>L(K,z)],options:U.value.map(z=>{var ee;return{label:z.name,value:z.key,disabled:((ee=r.configItems.map(c=>c.key))==null?void 0:ee.includes(z.key))&&z.key!==a.key}}),placeholder:"请选择配置参数",clearable:""},null,8,["value","onUpdate:value","options"])]),n("div",Ft,[l(e(xe),{value:a.value,"onUpdate:value":z=>a.value=z,placeholder:"参数值",precision:0},null,8,["value","onUpdate:value"])]),n("div",Ht,[l(e(O),{onClick:z=>f(K),type:"error",quaternary:"",circle:"",size:"small"},{icon:t(()=>[l(e(N),{component:e(Oe)},null,8,["component"])]),_:2},1032,["onClick"])])])]),_:2},1032,["label","path"]))),128))]),n("div",Jt,[l(e(O),{onClick:p,dashed:"",style:{width:"100%"},disabled:r.configItems.length>=U.value.length},{icon:t(()=>[l(e(N),{component:e(ze)},null,8,["component"])]),default:t(()=>[o[36]||(o[36]=i(" 添加配置参数 "))]),_:1,__:[36]},8,["disabled"])])]),n("div",Yt,[l(e(P),{path:"param_overrides"},{label:t(()=>[n("div",Wt,[o[38]||(o[38]=i(" 参数覆盖 ")),l(e(M),{trigger:"hover",placement:"top"},{trigger:t(()=>[l(e(N),{component:e(Q),class:"help-icon config-help"},null,8,["component"])]),default:t(()=>[o[37]||(o[37]=i(' 使用JSON格式定义要覆盖的API请求参数。例如： {"temperature": 0.7, "max_tokens": 2000}。这些参数会在发送请求时合并到原始参数中 '))]),_:1,__:[37]})])]),default:t(()=>[l(e(X),{value:r.param_overrides,"onUpdate:value":o[9]||(o[9]=a=>r.param_overrides=a),type:"textarea",placeholder:'{"temperature": 0.7, "max_tokens": 2000}',rows:4},null,8,["value"])]),_:1})])]),_:1})]),_:1})])]),_:1},8,["model"])]),_:1},8,["title"])]),_:1},8,["show"])}}}),Ve=ue(Xt,[["__scopeId","data-v-30dc8108"]]),Zt={class:"group-info-container"},el={class:"card-header"},tl={class:"header-left"},ll={class:"group-title"},ol={class:"header-actions"},nl={class:"stats-summary"},sl={class:"details-section"},al={class:"details-content"},rl={class:"detail-section"},il={class:"proxy-keys-content"},ul={class:"key-text"},dl={class:"description-content"},pl={class:"detail-section"},cl={class:"upstream-weight"},fl={key:0,class:"detail-section"},gl={class:"config-label"},vl={class:"config-tooltip"},ml={class:"tooltip-title"},_l={class:"tooltip-description"},yl={class:"tooltip-key"},hl={class:"config-json"},wl=j({__name:"GroupInfoCard",props:{group:{}},emits:["refresh","delete"],setup(D,{emit:d}){const s=D,I=d,y=b(null),h=b(!1),A=Me(),r=b(!1),R=b(!1),U=b([]),T=b([]),g=b(!1),_=he(()=>{var p;return(p=s.group)!=null&&p.proxy_keys?g.value?s.group.proxy_keys.replace(/,/g,`
`):lt(s.group.proxy_keys):"-"});async function x(){var L;if(!((L=s.group)!=null&&L.proxy_keys))return;const p=s.group.proxy_keys.replace(/,/g,`
`);await Ie(p)?window.$message.success("代理密钥已复制到剪贴板"):window.$message.error("复制失败")}De(()=>{$(),Y()}),le(()=>s.group,()=>{_e(),$()}),le(()=>J.groupDataRefreshTrigger,()=>{if(J.lastCompletedTask&&s.group){const p=J.lastCompletedTask.groupName===s.group.name,f=J.lastCompletedTask.taskType==="KEY_VALIDATION"||J.lastCompletedTask.taskType==="KEY_IMPORT";p&&f&&$()}}),le(()=>J.syncOperationTrigger,()=>{J.lastSyncOperation&&s.group&&J.lastSyncOperation.groupName===s.group.name&&$()});async function $(){var p,f;if(!((p=s.group)!=null&&p.id)){y.value=null;return}try{h.value=!0,(f=s.group)!=null&&f.id&&(y.value=await B.getGroupStats(s.group.id))}finally{h.value=!1}}async function Y(){try{const p=await B.getGroupConfigOptions();T.value=p||[]}catch(p){console.error("获取配置选项失败:",p)}}function oe(p){const f=T.value.find(L=>L.key===p);return(f==null?void 0:f.name)||p}function ae(p){const f=T.value.find(L=>L.key===p);return(f==null?void 0:f.description)||"暂无说明"}function de(){r.value=!0}function ve(p){r.value=!1,p&&I("refresh",p)}async function F(){if(!s.group||R.value)return;const p=A.warning({title:"删除分组",content:`确定要删除分组 "${we(s.group)}" 吗？此操作不可恢复。`,positiveText:"确定",negativeText:"取消",onPositiveClick:async()=>{var f;p.loading=!0,R.value=!0;try{(f=s.group)!=null&&f.id&&(await B.deleteGroup(s.group.id),I("delete",s.group))}catch(L){console.error("删除分组失败:",L)}finally{p.loading=!1,R.value=!1}}})}function te(p){return p>=1e3?`${(p/1e3).toFixed(1)}K`:p.toString()}function re(p){return p<=0?"0":`${(p*100).toFixed(1)}%`}async function me(p){if(!p)return;await Ie(p)?window.$message.success("地址已复制到剪贴板"):window.$message.error("复制失败")}function _e(){r.value=!1,U.value=[]}return(p,f)=>{const L=Z("n-divider"),H=Z("n-gradient-text"),se=Z("n-statistic"),k=Z("n-input");return v(),C("div",Zt,[l(e(ke),{bordered:!1,class:"group-info-card"},{header:t(()=>[n("div",el,[n("div",tl,[n("h3",ll,[i(w(p.group?e(we)(p.group):"请选择分组")+" ",1),p.group?(v(),V(e(M),{key:0,trigger:"hover"},{trigger:t(()=>[n("code",{class:"group-url",onClick:f[0]||(f[0]=o=>{var S;return me(((S=p.group)==null?void 0:S.endpoint)||"")})},w(p.group.endpoint),1)]),default:t(()=>[f[4]||(f[4]=i(" 点击复制 "))]),_:1,__:[4]})):ne("",!0)])]),n("div",ol,[l(e(O),{quaternary:"",circle:"",size:"small",onClick:de,title:"编辑分组"},{icon:t(()=>[l(e(N),{component:e(gt)},null,8,["component"])]),_:1}),l(e(O),{quaternary:"",circle:"",size:"small",onClick:F,title:"删除分组",type:"error",disabled:!p.group},{icon:t(()=>[l(e(N),{component:e(ht)},null,8,["component"])]),_:1},8,["disabled"])])])]),default:t(()=>[l(L,{style:{margin:"0","margin-bottom":"12px"}}),n("div",nl,[l(e(Te),{show:h.value,size:"small"},{default:t(()=>[l(e(Ne),{cols:4,"x-gap":12,"y-gap":12,responsive:"screen"},{default:t(()=>[l(e(W),{span:"1"},{default:t(()=>{var o,S;return[l(se,{label:`密钥数量：${((S=(o=y.value)==null?void 0:o.key_stats)==null?void 0:S.total_keys)??0}`},{default:t(()=>[l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"success",size:"20"},{default:t(()=>{var m,a;return[i(w(((a=(m=y.value)==null?void 0:m.key_stats)==null?void 0:a.active_keys)??0),1)]}),_:1})]),default:t(()=>[f[5]||(f[5]=i(" 有效密钥数 "))]),_:1,__:[5]}),l(L,{vertical:""}),l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"error",size:"20"},{default:t(()=>{var m,a;return[i(w(((a=(m=y.value)==null?void 0:m.key_stats)==null?void 0:a.invalid_keys)??0),1)]}),_:1})]),default:t(()=>[f[6]||(f[6]=i(" 无效密钥数 "))]),_:1,__:[6]})]),_:1},8,["label"])]}),_:1}),l(e(W),{span:"1"},{default:t(()=>{var o,S;return[l(se,{label:`1小时请求：${te(((S=(o=y.value)==null?void 0:o.hourly_stats)==null?void 0:S.total_requests)??0)}`},{default:t(()=>[l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"error",size:"20"},{default:t(()=>{var m,a;return[i(w(te(((a=(m=y.value)==null?void 0:m.hourly_stats)==null?void 0:a.failed_requests)??0)),1)]}),_:1})]),default:t(()=>[f[7]||(f[7]=i(" 近1小时失败请求 "))]),_:1,__:[7]}),l(L,{vertical:""}),l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"error",size:"20"},{default:t(()=>{var m,a;return[i(w(re(((a=(m=y.value)==null?void 0:m.hourly_stats)==null?void 0:a.failure_rate)??0)),1)]}),_:1})]),default:t(()=>[f[8]||(f[8]=i(" 近1小时失败率 "))]),_:1,__:[8]})]),_:1},8,["label"])]}),_:1}),l(e(W),{span:"1"},{default:t(()=>{var o,S;return[l(se,{label:`24小时请求：${te(((S=(o=y.value)==null?void 0:o.daily_stats)==null?void 0:S.total_requests)??0)}`},{default:t(()=>[l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"error",size:"20"},{default:t(()=>{var m,a;return[i(w(te(((a=(m=y.value)==null?void 0:m.daily_stats)==null?void 0:a.failed_requests)??0)),1)]}),_:1})]),default:t(()=>[f[9]||(f[9]=i(" 近24小时失败请求 "))]),_:1,__:[9]}),l(L,{vertical:""}),l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"error",size:"20"},{default:t(()=>{var m,a;return[i(w(re(((a=(m=y.value)==null?void 0:m.daily_stats)==null?void 0:a.failure_rate)??0)),1)]}),_:1})]),default:t(()=>[f[10]||(f[10]=i(" 近24小时失败率 "))]),_:1,__:[10]})]),_:1},8,["label"])]}),_:1}),l(e(W),{span:"1"},{default:t(()=>{var o,S;return[l(se,{label:`近7天请求：${te(((S=(o=y.value)==null?void 0:o.weekly_stats)==null?void 0:S.total_requests)??0)}`},{default:t(()=>[l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"error",size:"20"},{default:t(()=>{var m,a;return[i(w(te(((a=(m=y.value)==null?void 0:m.weekly_stats)==null?void 0:a.failed_requests)??0)),1)]}),_:1})]),default:t(()=>[f[11]||(f[11]=i(" 近7天失败请求 "))]),_:1,__:[11]}),l(L,{vertical:""}),l(e(M),{trigger:"hover"},{trigger:t(()=>[l(H,{type:"error",size:"20"},{default:t(()=>{var m,a;return[i(w(re(((a=(m=y.value)==null?void 0:m.weekly_stats)==null?void 0:a.failure_rate)??0)),1)]}),_:1})]),default:t(()=>[f[12]||(f[12]=i(" 近7天失败率 "))]),_:1,__:[12]})]),_:1},8,["label"])]}),_:1})]),_:1})]),_:1},8,["show"])]),l(L,{style:{margin:"0"}}),n("div",sl,[l(e(Fe),{accordion:"","expanded-names":U.value,"onUpdate:expandedNames":f[2]||(f[2]=o=>U.value=o)},{default:t(()=>[l(e(He),{title:"详细信息",name:"details"},{default:t(()=>{var o,S;return[n("div",al,[n("div",rl,[f[14]||(f[14]=n("h4",{class:"section-title"},"基础信息",-1)),l(e(Ce),{"label-placement":"left","label-width":"85px","label-align":"right"},{default:t(()=>[l(e(Ne),{cols:2},{default:t(()=>{var m;return[l(e(W),null,{default:t(()=>[l(e(P),{label:"分组名称："},{default:t(()=>{var a;return[i(w((a=p.group)==null?void 0:a.name),1)]}),_:1})]),_:1}),l(e(W),null,{default:t(()=>[l(e(P),{label:"显示名称："},{default:t(()=>{var a;return[i(w((a=p.group)==null?void 0:a.display_name),1)]}),_:1})]),_:1}),l(e(W),null,{default:t(()=>[l(e(P),{label:"渠道类型："},{default:t(()=>{var a;return[i(w((a=p.group)==null?void 0:a.channel_type),1)]}),_:1})]),_:1}),l(e(W),null,{default:t(()=>[l(e(P),{label:"排序："},{default:t(()=>{var a;return[i(w((a=p.group)==null?void 0:a.sort),1)]}),_:1})]),_:1}),l(e(W),null,{default:t(()=>[l(e(P),{label:"测试模型："},{default:t(()=>{var a;return[i(w((a=p.group)==null?void 0:a.test_model),1)]}),_:1})]),_:1}),((m=p.group)==null?void 0:m.channel_type)!=="gemini"?(v(),V(e(W),{key:0},{default:t(()=>[l(e(P),{label:"测试路径："},{default:t(()=>{var a;return[i(w((a=p.group)==null?void 0:a.validation_endpoint),1)]}),_:1})]),_:1})):ne("",!0),l(e(W),{span:2},{default:t(()=>[l(e(P),{label:"代理密钥："},{default:t(()=>{var a;return[n("div",il,[n("span",ul,w(_.value),1),(a=p.group)!=null&&a.proxy_keys?(v(),V(e(Je),{key:0,size:"small",class:"key-actions"},{default:t(()=>[l(e(M),{trigger:"hover"},{trigger:t(()=>[l(e(O),{quaternary:"",circle:"",onClick:f[1]||(f[1]=K=>g.value=!g.value)},{icon:t(()=>[l(e(N),{component:g.value?e(Ee):e(qe)},null,8,["component"])]),_:1})]),default:t(()=>[i(" "+w(g.value?"隐藏密钥":"显示密钥"),1)]),_:1}),l(e(M),{trigger:"hover"},{trigger:t(()=>[l(e(O),{quaternary:"",circle:"",onClick:x},{icon:t(()=>[l(e(N),{component:e(Be)},null,8,["component"])]),_:1})]),default:t(()=>[f[13]||(f[13]=i(" 复制密钥 "))]),_:1,__:[13]})]),_:1})):ne("",!0)])]}),_:1})]),_:1}),l(e(W),{span:2},{default:t(()=>[l(e(P),{label:"描述："},{default:t(()=>{var a;return[n("div",dl,w(((a=p.group)==null?void 0:a.description)||"-"),1)]}),_:1})]),_:1})]}),_:1})]),_:1})]),n("div",pl,[f[15]||(f[15]=n("h4",{class:"section-title"},"上游地址",-1)),l(e(Ce),{"label-placement":"left","label-width":"100px"},{default:t(()=>{var m;return[(v(!0),C(ge,null,fe(((m=p.group)==null?void 0:m.upstreams)??[],(a,K)=>(v(),V(e(P),{key:K,class:"upstream-item",label:`上游 ${K+1}:`},{default:t(()=>[n("span",cl,[l(e(Ue),{size:"small",type:"info"},{default:t(()=>[i("权重: "+w(a.weight),1)]),_:2},1024)]),l(k,{class:"upstream-url",value:a.url,readonly:"",size:"small"},null,8,["value"])]),_:2},1032,["label"]))),128))]}),_:1})]),(o=p.group)!=null&&o.config&&Object.keys(p.group.config).length>0||(S=p.group)!=null&&S.param_overrides?(v(),C("div",fl,[f[17]||(f[17]=n("h4",{class:"section-title"},"高级配置",-1)),l(e(Ce),{"label-placement":"left"},{default:t(()=>{var m,a;return[(v(!0),C(ge,null,fe(((m=p.group)==null?void 0:m.config)||{},(K,z)=>(v(),V(e(P),{key:z},{label:t(()=>[l(e(M),{trigger:"hover",delay:300,placement:"top"},{trigger:t(()=>[n("span",gl,[i(w(oe(z))+": ",1),l(e(N),{size:"14",class:"config-help-icon"},{default:t(()=>f[16]||(f[16]=[n("svg",{viewBox:"0 0 24 24"},[n("path",{fill:"currentColor",d:"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,17A1.5,1.5 0 0,1 10.5,15.5A1.5,1.5 0 0,1 12,14A1.5,1.5 0 0,1 13.5,15.5A1.5,1.5 0 0,1 12,17M12,10.5C10.07,10.5 8.5,8.93 8.5,7A3.5,3.5 0 0,1 12,3.5A3.5,3.5 0 0,1 15.5,7C15.5,8.93 13.93,10.5 12,10.5Z"})],-1)])),_:1,__:[16]})])]),default:t(()=>[n("div",vl,[n("div",ml,w(oe(z)),1),n("div",_l,w(ae(z)),1),n("div",yl,"配置键: "+w(z),1)])]),_:2},1024)]),default:t(()=>[i(" "+w(K||"-"),1)]),_:2},1024))),128)),(a=p.group)!=null&&a.param_overrides?(v(),V(e(P),{key:0,label:"参数覆盖:",span:2},{default:t(()=>{var K;return[n("pre",hl,w(JSON.stringify(((K=p.group)==null?void 0:K.param_overrides)||"",null,2)),1)]}),_:1})):ne("",!0)]}),_:1})])):ne("",!0)])]}),_:1})]),_:1},8,["expanded-names"])])]),_:1}),l(Ve,{show:r.value,"onUpdate:show":f[3]||(f[3]=o=>r.value=o),group:p.group,onSuccess:ve},null,8,["show","group"])])}}}),kl=ue(wl,[["__scopeId","data-v-8185acb2"]]),bl={class:"group-list-container"},Cl={class:"search-section"},$l={class:"groups-section"},xl={key:0,class:"empty-container"},Gl={key:1,class:"groups-list"},Il=["onClick"],zl={class:"group-icon"},Al={key:0},Tl={key:1},Sl={key:2},Nl={key:3},Ol={class:"group-content"},Kl={class:"group-name"},Ml={class:"group-meta"},Dl={class:"group-id"},Ul={class:"add-section"},Pl=j({__name:"GroupList",props:{groups:{},selectedGroup:{},loading:{type:Boolean,default:!1}},emits:["group-select","refresh","refresh-and-select"],setup(D,{emit:d}){const s=D,I=d,y=b(""),h=b(!1),A=he(()=>{if(!y.value)return s.groups;const g=y.value.toLowerCase();return s.groups.filter(_=>_.name.toLowerCase().includes(g)||_.display_name&&_.display_name.toLowerCase().includes(g))});function r(g){I("group-select",g)}function R(g){switch(g){case"openai":return"success";case"gemini":return"info";case"anthropic":return"warning";default:return"default"}}function U(){h.value=!0}function T(g){h.value=!1,g&&g.id&&I("refresh-and-select",g.id)}return(g,_)=>{const x=Z("n-icon");return v(),C("div",bl,[l(e(ke),{class:"group-list-card modern-card",bordered:!1,size:"small"},{default:t(()=>[n("div",Cl,[l(e(X),{value:y.value,"onUpdate:value":_[0]||(_[0]=$=>y.value=$),placeholder:"搜索分组名称...",size:"small",clearable:""},{prefix:t(()=>[l(x,{component:e(Le)},null,8,["component"])]),_:1},8,["value"])]),n("div",$l,[l(e(Te),{show:g.loading,size:"small"},{default:t(()=>[A.value.length===0&&!g.loading?(v(),C("div",xl,[l(e(Pe),{size:"small",description:y.value?"未找到匹配的分组":"暂无分组"},null,8,["description"])])):(v(),C("div",Gl,[(v(!0),C(ge,null,fe(A.value,$=>{var Y;return v(),C("div",{key:$.id,class:Re(["group-item",{active:((Y=g.selectedGroup)==null?void 0:Y.id)===$.id}]),onClick:oe=>r($)},[n("div",zl,[$.channel_type==="openai"?(v(),C("span",Al,"🤖")):$.channel_type==="gemini"?(v(),C("span",Tl,"💎")):$.channel_type==="anthropic"?(v(),C("span",Sl,"🧠")):(v(),C("span",Nl,"🔧"))]),n("div",Ol,[n("div",Kl,w(e(we)($)),1),n("div",Ml,[l(e(Ue),{size:"tiny",type:R($.channel_type)},{default:t(()=>[i(w($.channel_type),1)]),_:2},1032,["type"]),n("span",Dl,"#"+w($.name),1)])])],10,Il)}),128))]))]),_:1},8,["show"])]),n("div",Ul,[l(e(O),{type:"primary",size:"small",block:"",onClick:U},{icon:t(()=>[l(x,{component:e(ze)},null,8,["component"])]),default:t(()=>[_[2]||(_[2]=i(" 创建分组 "))]),_:1,__:[2]})])]),_:1}),l(Ve,{show:h.value,"onUpdate:show":_[1]||(_[1]=$=>h.value=$),onSuccess:T},null,8,["show"])])}}}),Rl=ue(Pl,[["__scopeId","data-v-814ce7e7"]]),El={style:{display:"flex","justify-content":"flex-end",gap:"12px"}},ql=j({__name:"KeyCreateDialog",props:{show:{type:Boolean},groupId:{},groupName:{}},emits:["update:show","success"],setup(D,{emit:d}){const s=D,I=d,y=b(!1),h=b("");le(()=>s.show,U=>{U&&A()});function A(){h.value=""}function r(){I("update:show",!1)}async function R(){if(!(y.value||!h.value.trim()))try{y.value=!0,await B.addKeysAsync(s.groupId,h.value),A(),r(),window.$message.success("密钥导入任务已开始，请稍后在下方查看进度。"),J.taskPollingTrigger++}finally{y.value=!1}}return(U,T)=>{const g=Z("n-icon");return v(),V(e(Ae),{show:U.show,"onUpdate:show":r,class:"form-modal"},{default:t(()=>[l(e(ke),{style:{width:"800px"},title:`为 ${U.groupName||"当前分组"} 添加密钥`,bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{"header-extra":t(()=>[l(e(O),{quaternary:"",circle:"",onClick:r},{icon:t(()=>[l(g,{component:e(Se)},null,8,["component"])]),_:1})]),footer:t(()=>[n("div",El,[l(e(O),{onClick:r},{default:t(()=>T[1]||(T[1]=[i("取消")])),_:1,__:[1]}),l(e(O),{type:"primary",onClick:R,loading:y.value,disabled:!h.value},{default:t(()=>T[2]||(T[2]=[i(" 创建 ")])),_:1,__:[2]},8,["loading","disabled"])])]),default:t(()=>[l(e(X),{value:h.value,"onUpdate:value":T[0]||(T[0]=_=>h.value=_),type:"textarea",placeholder:"输入密钥，每行一个",rows:8,style:{"margin-top":"20px"}},null,8,["value"])]),_:1},8,["title"])]),_:1},8,["show"])}}}),Ll=ue(ql,[["__scopeId","data-v-6b51ddfc"]]),Bl={style:{display:"flex","justify-content":"flex-end",gap:"12px"}},Vl=j({__name:"KeyDeleteDialog",props:{show:{type:Boolean},groupId:{},groupName:{}},emits:["update:show","success"],setup(D,{emit:d}){const s=D,I=d,y=b(!1),h=b(""),A=Ke();le(()=>s.show,T=>{T&&r()});function r(){h.value=""}function R(){I("update:show",!1)}async function U(){if(!(y.value||!h.value.trim()))try{y.value=!0;const T=await B.deleteKeys(s.groupId,h.value),{deleted_count:g,ignored_count:_,total_in_group:x}=T||{},$=`成功删除 ${g} 个密钥，忽略 ${_} 个密钥。当前分组共有 ${x} 个密钥。`;A.info($,{closable:!0,duration:5e3}),I("success"),R()}finally{y.value=!1}}return(T,g)=>{const _=Z("n-icon");return v(),V(e(Ae),{show:T.show,"onUpdate:show":R,class:"form-modal"},{default:t(()=>[l(e(ke),{style:{width:"800px"},title:`删除 ${T.groupName||"当前分组"} 的密钥`,bordered:!1,size:"huge",role:"dialog","aria-modal":"true"},{"header-extra":t(()=>[l(e(O),{quaternary:"",circle:"",onClick:R},{icon:t(()=>[l(_,{component:e(Se)},null,8,["component"])]),_:1})]),footer:t(()=>[n("div",Bl,[l(e(O),{onClick:R},{default:t(()=>g[1]||(g[1]=[i("取消")])),_:1,__:[1]}),l(e(O),{type:"error",onClick:U,loading:y.value,disabled:!h.value},{default:t(()=>g[2]||(g[2]=[i(" 删除 ")])),_:1,__:[2]},8,["loading","disabled"])])]),default:t(()=>[l(e(X),{value:h.value,"onUpdate:value":g[0]||(g[0]=x=>h.value=x),type:"textarea",placeholder:"输入要删除的密钥，每行一个",rows:8,style:{"margin-top":"20px"}},null,8,["value"])]),_:1},8,["title"])]),_:1},8,["show"])}}}),jl=ue(Vl,[["__scopeId","data-v-05492870"]]),Fl={class:"key-table-container"},Hl={class:"toolbar"},Jl={class:"toolbar-left"},Yl={class:"toolbar-right"},Wl={class:"keys-grid-container"},Ql={key:0,class:"empty-container"},Xl={key:1,class:"keys-grid"},Zl={class:"key-main"},eo={class:"key-section"},to={class:"quick-actions"},lo={class:"key-bottom"},oo={class:"key-stats"},no={class:"stat-item"},so={class:"stat-item"},ao={class:"stat-item"},ro={class:"pagination-container"},io={class:"pagination-info"},uo={class:"pagination-controls"},po={class:"page-info"},co=j({__name:"KeyTable",props:{selectedGroup:{}},setup(D){const d=D,s=b([]),I=b(!1),y=b(""),h=b("all"),A=b(1),r=b(12),R=b(0),U=b(0),T=Me(),g=[{label:"全部",value:"all"},{label:"有效",value:"active"},{label:"无效",value:"invalid"}],_=[{label:"导出所有密钥",key:"copyAll"},{label:"导出有效密钥",key:"copyValid"},{label:"导出无效密钥",key:"copyInvalid"},{type:"divider"},{label:"恢复所有无效密钥",key:"restoreAll"},{label:"清空所有无效密钥",key:"clearInvalid",props:{style:{color:"#d03050"}}},{type:"divider"},{label:"验证所有密钥",key:"validateAll"}];let x=null;const $=b(!1),Y=b(!1),oe=b(!1),ae=b(!1);le(()=>d.selectedGroup,async c=>{if(c){const u=A.value!==1||h.value!=="all";ee(),u||await F()}},{immediate:!0}),le([A,r,h],async()=>{await F()}),le(()=>J.groupDataRefreshTrigger,()=>{if(J.lastCompletedTask&&d.selectedGroup){const c=J.lastCompletedTask.groupName===d.selectedGroup.name,u=J.lastCompletedTask.taskType==="KEY_VALIDATION"||J.lastCompletedTask.taskType==="KEY_IMPORT";c&&u&&F()}});function de(){A.value=1,F()}function ve(c){switch(c){case"copyAll":se();break;case"copyValid":k();break;case"copyInvalid":o();break;case"restoreAll":S();break;case"validateAll":m();break;case"clearInvalid":a();break}}async function F(){var c;if((c=d.selectedGroup)!=null&&c.id)try{I.value=!0;const u=await B.getGroupKeys({group_id:d.selectedGroup.id,page:A.value,page_size:r.value,status:h.value==="all"?void 0:h.value,key:y.value.trim()||void 0});s.value=u.items,R.value=u.pagination.total_items,U.value=u.pagination.total_pages}catch{window.$message.error("加载密钥失败")}finally{I.value=!1}}async function te(){await F(),d.selectedGroup&&ce(d.selectedGroup.name,"BATCH_DELETE")}async function re(c){await Ie(c.key_value)?window.$message.success("密钥已复制到剪贴板"):window.$message.error("复制失败")}async function me(c){var u;if(!(!((u=d.selectedGroup)!=null&&u.id)||!c.key_value||x)){x=window.$message.info("正在测试密钥...",{duration:0});try{const q=await B.testKeys(d.selectedGroup.id,c.key_value),E=(q==null?void 0:q[0])||{};E.is_valid?window.$message.success("密钥测试成功"):window.$message.error(E.error||"密钥测试失败: 无效的API密钥",{keepAliveOnHover:!0,duration:5e3,closable:!0}),await F(),ce(d.selectedGroup.name,"TEST_SINGLE")}catch{console.error("测试失败")}finally{x==null||x.destroy(),x=null}}}function _e(c){c.is_visible=!c.is_visible}async function p(c){var q;if(!((q=d.selectedGroup)!=null&&q.id)||!c.key_value||Y.value)return;const u=T.warning({title:"恢复密钥",content:`确定要恢复密钥"${Ge(c.key_value)}"吗？`,positiveText:"确定",negativeText:"取消",onPositiveClick:async()=>{var E;if((E=d.selectedGroup)!=null&&E.id){Y.value=!0,u.loading=!0;try{await B.restoreKeys(d.selectedGroup.id,c.key_value),await F(),ce(d.selectedGroup.name,"RESTORE_SINGLE")}catch{console.error("恢复失败")}finally{u.loading=!1,Y.value=!1}}}})}async function f(c){var q;if(!((q=d.selectedGroup)!=null&&q.id)||!c.key_value||$.value)return;const u=T.warning({title:"删除密钥",content:`确定要删除密钥"${Ge(c.key_value)}"吗？`,positiveText:"确定",negativeText:"取消",onPositiveClick:async()=>{var E;if((E=d.selectedGroup)!=null&&E.id){u.loading=!0,$.value=!0;try{await B.deleteKeys(d.selectedGroup.id,c.key_value),await F(),ce(d.selectedGroup.name,"DELETE_SINGLE")}catch{console.error("删除失败")}finally{u.loading=!1,$.value=!1}}}})}function L(c){if(!c)return"从未";const u=new Date,q=new Date(c),E=Math.floor((u.getTime()-q.getTime())/1e3),ie=Math.floor(E/60),pe=Math.floor(ie/60),ye=Math.floor(pe/24);return ye>0?`${ye}天前`:pe>0?`${pe}小时前`:ie>0?`${ie}分钟前`:E>0?`${E}秒前`:"刚刚"}function H(c){switch(c){case"active":return"status-valid";case"invalid":return"status-invalid";default:return"status-unknown"}}async function se(){var c;(c=d.selectedGroup)!=null&&c.id&&B.exportKeys(d.selectedGroup.id,"all")}async function k(){var c;(c=d.selectedGroup)!=null&&c.id&&B.exportKeys(d.selectedGroup.id,"active")}async function o(){var c;(c=d.selectedGroup)!=null&&c.id&&B.exportKeys(d.selectedGroup.id,"invalid")}async function S(){var u;if(!((u=d.selectedGroup)!=null&&u.id)||Y.value)return;const c=T.warning({title:"恢复密钥",content:"确定要恢复所有无效密钥吗？",positiveText:"确定",negativeText:"取消",onPositiveClick:async()=>{var q;if((q=d.selectedGroup)!=null&&q.id){Y.value=!0,c.loading=!0;try{await B.restoreAllInvalidKeys(d.selectedGroup.id),await F(),ce(d.selectedGroup.name,"RESTORE_ALL_INVALID")}catch{console.error("恢复失败")}finally{c.loading=!1,Y.value=!1}}}})}async function m(){var c;if(!(!((c=d.selectedGroup)!=null&&c.id)||x)){x=window.$message.info("正在验证密钥...",{duration:0});try{await B.validateGroupKeys(d.selectedGroup.id),localStorage.removeItem("last_closed_task"),J.taskPollingTrigger++}catch{console.error("测试失败")}finally{x==null||x.destroy(),x=null}}}async function a(){var u;if(!((u=d.selectedGroup)!=null&&u.id)||$.value)return;const c=T.warning({title:"清除密钥",content:"确定要清除所有无效密钥吗？此操作不可恢复！",positiveText:"确定",negativeText:"取消",onPositiveClick:async()=>{var q;if((q=d.selectedGroup)!=null&&q.id){$.value=!0,c.loading=!0;try{const{data:E}=await B.clearAllInvalidKeys(d.selectedGroup.id);window.$message.success((E==null?void 0:E.message)||"清除成功"),await F(),ce(d.selectedGroup.name,"CLEAR_ALL_INVALID")}catch{console.error("删除失败")}finally{c.loading=!1,$.value=!1}}}})}function K(c){A.value=c}function z(c){r.value=c,A.value=1}function ee(){A.value=1,y.value="",h.value="all"}return(c,u)=>{var pe,ye;const q=Z("n-input-group"),E=Z("n-tag"),ie=Z("n-button-group");return v(),C("div",Fl,[n("div",Hl,[n("div",Jl,[l(e(O),{type:"success",size:"small",onClick:u[0]||(u[0]=G=>oe.value=!0)},{icon:t(()=>[l(e(N),{component:e(st)},null,8,["component"])]),default:t(()=>[u[9]||(u[9]=i(" 添加密钥 "))]),_:1,__:[9]}),l(e(O),{type:"error",size:"small",onClick:u[1]||(u[1]=G=>ae.value=!0)},{icon:t(()=>[l(e(N),{component:e(_t)},null,8,["component"])]),default:t(()=>[u[10]||(u[10]=i(" 删除密钥 "))]),_:1,__:[10]})]),n("div",Yl,[l(e(Ye),{size:12},{default:t(()=>[l(e($e),{value:h.value,"onUpdate:value":u[2]||(u[2]=G=>h.value=G),options:g,size:"small",style:{width:"100px"}},null,8,["value"]),l(q,null,{default:t(()=>[l(e(X),{value:y.value,"onUpdate:value":u[3]||(u[3]=G=>y.value=G),placeholder:"Key 模糊查询",size:"small",style:{width:"180px"},clearable:"",onKeyup:We(de,["enter"])},null,8,["value"]),l(e(O),{ghost:"",size:"small",disabled:I.value,onClick:de},{default:t(()=>[l(e(N),{component:e(Le)},null,8,["component"])]),_:1},8,["disabled"])]),_:1}),l(e(Qe),{options:_,trigger:"click",onSelect:ve},{default:t(()=>[l(e(O),{size:"small",secondary:""},{icon:t(()=>u[11]||(u[11]=[n("span",{style:{"font-size":"16px","font-weight":"bold"}},"⋯",-1)])),_:1})]),_:1})]),_:1})])]),n("div",Wl,[l(e(Te),{show:I.value},{default:t(()=>[s.value.length===0&&!I.value?(v(),C("div",Ql,[l(e(Pe),{description:"没有找到匹配的密钥"})])):(v(),C("div",Xl,[(v(!0),C(ge,null,fe(s.value,G=>(v(),C("div",{key:G.id,class:Re(["key-card",H(G.status)])},[n("div",Zl,[n("div",eo,[G.status==="active"?(v(),V(E,{key:0,type:"success",bordered:!1,round:""},{icon:t(()=>[l(e(N),{component:e(ut)},null,8,["component"])]),default:t(()=>[u[12]||(u[12]=i(" 有效 "))]),_:1,__:[12]})):(v(),V(E,{key:1,bordered:!1,round:""},{icon:t(()=>[l(e(N),{component:e(rt)},null,8,["component"])]),default:t(()=>[u[13]||(u[13]=i(" 无效 "))]),_:1,__:[13]})),l(e(X),{class:"key-text",value:G.is_visible?G.key_value:e(Ge)(G.key_value),readonly:"",size:"small"},null,8,["value"]),n("div",to,[l(e(O),{size:"tiny",text:"",onClick:be=>_e(G),title:"显示/隐藏"},{icon:t(()=>[l(e(N),{component:G.is_visible?e(Ee):e(qe)},null,8,["component"])]),_:2},1032,["onClick"]),l(e(O),{size:"tiny",text:"",onClick:be=>re(G),title:"复制"},{icon:t(()=>[l(e(N),{component:e(Be)},null,8,["component"])]),_:2},1032,["onClick"])])])]),n("div",lo,[n("div",oo,[n("span",no,[u[14]||(u[14]=i(" 请求 ")),n("strong",null,w(G.request_count),1)]),n("span",so,[u[15]||(u[15]=i(" 失败 ")),n("strong",null,w(G.failure_count),1)]),n("span",ao,w(G.last_used_at?L(G.last_used_at):"未使用"),1)]),l(ie,{class:"key-actions"},{default:t(()=>[l(e(O),{round:"",tertiary:"",type:"info",size:"tiny",onClick:be=>me(G),title:"测试密钥"},{default:t(()=>u[16]||(u[16]=[i(" 测试 ")])),_:2,__:[16]},1032,["onClick"]),G.status!=="active"?(v(),V(e(O),{key:0,tertiary:"",size:"tiny",onClick:be=>p(G),title:"恢复密钥",type:"warning"},{default:t(()=>u[17]||(u[17]=[i(" 恢复 ")])),_:2,__:[17]},1032,["onClick"])):ne("",!0),l(e(O),{round:"",tertiary:"",size:"tiny",type:"error",onClick:be=>f(G),title:"删除密钥"},{default:t(()=>u[18]||(u[18]=[i(" 删除 ")])),_:2,__:[18]},1032,["onClick"])]),_:2},1024)])],2))),128))]))]),_:1},8,["show"])]),n("div",ro,[n("div",io,[n("span",null,"共 "+w(R.value)+" 条记录",1),l(e($e),{value:r.value,"onUpdate:value":[u[4]||(u[4]=G=>r.value=G),z],options:[{label:"12条/页",value:12},{label:"24条/页",value:24},{label:"60条/页",value:60},{label:"120条/页",value:120}],size:"small",style:{width:"100px","margin-left":"12px"}},null,8,["value"])]),n("div",uo,[l(e(O),{size:"small",disabled:A.value<=1,onClick:u[5]||(u[5]=G=>K(A.value-1))},{default:t(()=>u[19]||(u[19]=[i(" 上一页 ")])),_:1,__:[19]},8,["disabled"]),n("span",po,"第 "+w(A.value)+" 页，共 "+w(U.value)+" 页",1),l(e(O),{size:"small",disabled:A.value>=U.value,onClick:u[6]||(u[6]=G=>K(A.value+1))},{default:t(()=>u[20]||(u[20]=[i(" 下一页 ")])),_:1,__:[20]},8,["disabled"])])]),(pe=c.selectedGroup)!=null&&pe.id?(v(),V(Ll,{key:0,show:oe.value,"onUpdate:show":u[7]||(u[7]=G=>oe.value=G),"group-id":c.selectedGroup.id,"group-name":e(we)(c.selectedGroup),onSuccess:F},null,8,["show","group-id","group-name"])):ne("",!0),(ye=c.selectedGroup)!=null&&ye.id?(v(),V(jl,{key:1,show:ae.value,"onUpdate:show":u[8]||(u[8]=G=>ae.value=G),"group-id":c.selectedGroup.id,"group-name":e(we)(c.selectedGroup),onSuccess:te},null,8,["show","group-id","group-name"])):ne("",!0)])}}}),fo=ue(co,[["__scopeId","data-v-313c8f1b"]]),go={class:"keys-container"},vo={class:"sidebar"},mo={class:"main-content"},_o={class:"group-info"},yo={class:"key-table-section"},ho=j({__name:"Keys",setup(D){const d=b([]),s=b(!1),I=b(null),y=Ze(),h=Xe();De(async()=>{await A()});async function A(){try{if(s.value=!0,d.value=await B.getGroups(),d.value.length>0&&!I.value){const g=h.query.groupId,_=d.value.find(x=>String(x.id)===String(g));_?I.value=_:r(d.value[0])}}finally{s.value=!1}}function r(g){I.value=g||null,String(g==null?void 0:g.id)!==String(h.query.groupId)&&y.push({name:"keys",query:{groupId:(g==null?void 0:g.id)||""}})}async function R(){await A(),I.value&&r(d.value.find(g=>{var _;return g.id===((_=I.value)==null?void 0:_.id)})||null)}async function U(g){await A();const _=d.value.find(x=>x.id===g);_&&r(_)}function T(g){var _;d.value=d.value.filter(x=>x.id!==g.id),((_=I.value)==null?void 0:_.id)===g.id&&r(d.value.length>0?d.value[0]:null)}return(g,_)=>(v(),C("div",go,[n("div",vo,[l(Rl,{groups:d.value,"selected-group":I.value,loading:s.value,onGroupSelect:r,onRefresh:R,onRefreshAndSelect:U},null,8,["groups","selected-group","loading"])]),n("div",mo,[n("div",_o,[l(kl,{group:I.value,onRefresh:R,onDelete:T},null,8,["group"])]),n("div",yo,[l(fo,{"selected-group":I.value},null,8,["selected-group"])])])]))}}),$o=ue(ho,[["__scopeId","data-v-99e363bc"]]);export{$o as default};
