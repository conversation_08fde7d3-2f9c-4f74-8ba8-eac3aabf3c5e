import{d as N,c as D,g as a,p as L,h as z,r as y,q as A,B as H,o as G,s as R,a as i,u as l,N as I,w as _,Y as U,v as $,G as w,T as b,J as g,k as f,H as S,z as F,Z as J,$ as u,t as C,l as K,a0 as h,_ as Y}from"./index-C-we6bTS.js";import{a as Z}from"./display-DuIJKIJ5.js";import{S as Q,E as W,a as X}from"./Search-LBeTRPTK.js";const ee={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},te=N({name:"DownloadOutline",render:function(c,d){return L(),D("svg",ee,d[0]||(d[0]=[a("path",{d:"M336 176h40a40 40 0 0 1 40 40v208a40 40 0 0 1-40 40H136a40 40 0 0 1-40-40V216a40 40 0 0 1 40-40h40",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),a("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 272l80 80l80-80"},null,-1),a("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 48v288"},null,-1)]))}}),P={getLogs:m=>z.get("/logs",{params:m}),getGroups:()=>z.get("/groups"),exportLogs:m=>{const c=localStorage.getItem("authKey");if(!c){window.$message.error("未找到认证信息，无法导出");return}const d=new URLSearchParams(Object.entries(m).reduce((p,[k,e])=>(e!=null&&e!==""&&(p[k]=String(e)),p),{}));d.append("key",c);const n=`${z.defaults.baseURL}/logs/export?${d.toString()}`,r=document.createElement("a");r.href=n,r.setAttribute("download",`logs-${Date.now()}.csv`),document.body.appendChild(r),r.click(),document.body.removeChild(r)}},se={class:"log-table-container"},ae={class:"toolbar"},le={class:"filter-section"},oe={class:"filter-row"},ie={class:"filter-group"},ne={class:"filter-group"},re={class:"filter-group"},ue={class:"filter-group"},de={class:"filter-group"},ce={class:"filter-group"},_e={class:"filter-row"},pe={class:"filter-group"},ve={class:"filter-actions"},me={class:"table-main"},ge={class:"table-container"},ye={class:"pagination-container"},fe={class:"pagination-info"},he={class:"pagination-controls"},ke={class:"page-info"},we=N({__name:"LogTable",setup(m){const c=y(!1),d=y([]),n=y(1),r=y(15),p=y(0),k=A(()=>Math.ceil(p.value/r.value)),e=H({group_name:"",key_value:"",is_success:"",status_code:"",source_ip:"",error_contains:"",start_time:null,end_time:null}),T=[{label:"状态",value:""},{label:"成功",value:"true"},{label:"失败",value:"false"}],x=async()=>{c.value=!0;try{const t={page:n.value,page_size:r.value,group_name:e.group_name||void 0,key_value:e.key_value||void 0,is_success:e.is_success===""?void 0:e.is_success==="true",status_code:e.status_code?parseInt(e.status_code,10):void 0,source_ip:e.source_ip||void 0,error_contains:e.error_contains||void 0,start_time:e.start_time?new Date(e.start_time).toISOString():void 0,end_time:e.end_time?new Date(e.end_time).toISOString():void 0},s=await P.getLogs(t);s.code===0&&s.data?(d.value=s.data.items.map(o=>({...o,is_key_visible:!1})),p.value=s.data.pagination.total_items):(d.value=[],p.value=0,window.$message.error(s.message||"加载日志失败",{keepAliveOnHover:!0,duration:5e3,closable:!0}))}catch{window.$message.error("加载日志请求失败")}finally{c.value=!1}},B=t=>t?new Date(t).toLocaleString("zh-CN",{hour12:!1}).replace(/\//g,"-"):"-",E=t=>{t.is_key_visible=!t.is_key_visible},M=[{title:"时间",key:"timestamp",width:160,render:t=>B(t.timestamp)},{title:"状态",key:"is_success",width:50,render:t=>u(K,{type:t.is_success?"success":"error",size:"small",round:!0},{default:()=>t.is_success?"成功":"失败"})},{title:"类型",key:"is_stream",width:50,render:t=>u(K,{type:t.is_stream?"info":"default",size:"small",round:!0},{default:()=>t.is_stream?"流式":"非流"})},{title:"状态码",key:"status_code",width:60},{title:"耗时(ms)",key:"duration_ms",width:80},{title:"重试",key:"retries",width:50},{title:"分组",key:"group_name",width:120},{title:"Key",key:"key_value",width:200,render:t=>u(I,{align:"center",wrap:!1},()=>[u(h,{style:"max-width: 150px"},{default:()=>t.is_key_visible?t.key_value:Z(t.key_value||"")}),u(g,{size:"tiny",text:!0,onClick:()=>E(t)},{icon:()=>u(S,null,{default:()=>u(t.is_key_visible?W:X)})})])},{title:"请求路径",key:"request_path",width:220,render:t=>u(h,{style:"max-width: 200px"},{default:()=>t.request_path})},{title:"上游地址",key:"upstream_addr",width:220,render:t=>u(h,{style:"max-width: 200px"},{default:()=>t.upstream_addr})},{title:"源IP",key:"source_ip",width:140},{title:"错误信息",width:270,key:"error_message",render:t=>u(h,{style:"max-width: 250px"},{default:()=>t.error_message||"-"})},{title:"User Agent",key:"user_agent",width:220,render:t=>u(h,{style:"max-width: 200px"},{default:()=>t.user_agent})}];G(x),R([n,r],x);const v=()=>{n.value=1,x()},V=()=>{e.group_name="",e.key_value="",e.is_success="",e.status_code="",e.source_ip="",e.error_contains="",e.start_time=null,e.end_time=null,v()},j=()=>{const t={group_name:e.group_name||void 0,key_value:e.key_value||void 0,is_success:e.is_success===""?void 0:e.is_success==="true",status_code:e.status_code?parseInt(e.status_code,10):void 0,source_ip:e.source_ip||void 0,error_contains:e.error_contains||void 0,start_time:e.start_time?new Date(e.start_time).toISOString():void 0,end_time:e.end_time?new Date(e.end_time).toISOString():void 0};P.exportLogs(t)};function O(t){n.value=t}function q(t){r.value=t,n.value=1}return(t,s)=>(L(),D("div",se,[i(l(I),{vertical:""},{default:_(()=>[a("div",ae,[a("div",le,[a("div",oe,[a("div",ie,[i(l(U),{value:e.start_time,"onUpdate:value":s[0]||(s[0]=o=>e.start_time=o),type:"datetime",clearable:"",size:"small",placeholder:"开始时间",style:{width:"180px"}},null,8,["value"])]),a("div",ne,[i(l(U),{value:e.end_time,"onUpdate:value":s[1]||(s[1]=o=>e.end_time=o),type:"datetime",clearable:"",size:"small",placeholder:"结束时间",style:{width:"180px"}},null,8,["value"])]),a("div",re,[i(l($),{value:e.is_success,"onUpdate:value":[s[2]||(s[2]=o=>e.is_success=o),v],options:T,size:"small",style:{width:"166px"}},null,8,["value"])]),a("div",ue,[i(l(w),{value:e.status_code,"onUpdate:value":s[3]||(s[3]=o=>e.status_code=o),placeholder:"状态码",size:"small",clearable:"",style:{width:"166px"},onKeyup:b(v,["enter"])},null,8,["value"])]),a("div",de,[i(l(w),{value:e.group_name,"onUpdate:value":s[4]||(s[4]=o=>e.group_name=o),placeholder:"分组名",size:"small",clearable:"",style:{width:"166px"},onKeyup:b(v,["enter"])},null,8,["value"])]),a("div",ce,[i(l(w),{value:e.key_value,"onUpdate:value":s[5]||(s[5]=o=>e.key_value=o),placeholder:"密钥",size:"small",clearable:"",style:{width:"166px"},onKeyup:b(v,["enter"])},null,8,["value"])])]),a("div",_e,[a("div",pe,[i(l(w),{value:e.error_contains,"onUpdate:value":s[6]||(s[6]=o=>e.error_contains=o),placeholder:"错误信息",size:"small",clearable:"",style:{width:"384px"},onKeyup:b(v,["enter"])},null,8,["value"])]),a("div",ve,[i(l(g),{ghost:"",size:"small",disabled:c.value,onClick:v},{icon:_(()=>[i(l(S),{component:l(Q)},null,8,["component"])]),default:_(()=>[s[10]||(s[10]=f(" 搜索 "))]),_:1,__:[10]},8,["disabled"]),i(l(g),{size:"small",onClick:V},{default:_(()=>s[11]||(s[11]=[f("重置")])),_:1,__:[11]}),i(l(g),{size:"small",type:"primary",ghost:"",onClick:j},{icon:_(()=>[i(l(S),{component:l(te)},null,8,["component"])]),default:_(()=>[s[12]||(s[12]=f(" 导出密钥 "))]),_:1,__:[12]})])])])]),a("div",me,[a("div",ge,[i(l(F),{show:c.value},{default:_(()=>[i(l(J),{columns:l(M),data:d.value,bordered:!1,remote:"",size:"small"},null,8,["columns","data"])]),_:1},8,["show"])]),a("div",ye,[a("div",fe,[a("span",null,"共 "+C(p.value)+" 条记录",1),i(l($),{value:r.value,"onUpdate:value":[s[7]||(s[7]=o=>r.value=o),q],options:[{label:"15条/页",value:15},{label:"30条/页",value:30},{label:"50条/页",value:50},{label:"100条/页",value:100}],size:"small",style:{width:"100px","margin-left":"12px"}},null,8,["value"])]),a("div",he,[i(l(g),{size:"small",disabled:n.value<=1,onClick:s[8]||(s[8]=o=>O(n.value-1))},{default:_(()=>s[13]||(s[13]=[f(" 上一页 ")])),_:1,__:[13]},8,["disabled"]),a("span",ke,"第 "+C(n.value)+" 页，共 "+C(k.value)+" 页",1),i(l(g),{size:"small",disabled:n.value>=k.value,onClick:s[9]||(s[9]=o=>O(n.value+1))},{default:_(()=>s[14]||(s[14]=[f(" 下一页 ")])),_:1,__:[14]},8,["disabled"])])])])]),_:1})]))}}),be=Y(we,[["__scopeId","data-v-e8e3e184"]]),Ne=N({__name:"Logs",setup(m){return(c,d)=>(L(),D("div",null,[i(be)]))}});export{Ne as default};
