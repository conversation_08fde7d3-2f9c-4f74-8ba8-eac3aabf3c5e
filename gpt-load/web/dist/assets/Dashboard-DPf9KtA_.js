import{h as Q,d as tt,r as f,o as nt,c as u,a as h,u as d,N as J,w as _,b as pt,e as U,f as Z,g as e,i as K,j as P,k as A,t as y,l as R,m as ft,n as w,p as c,_ as et,q as W,s as yt,v as mt,F as T,x as z,y as gt,z as xt}from"./index-C-we6bTS.js";import{g as bt}from"./display-DuIJKIJ5.js";const kt=()=>Q.get("/dashboard/stats"),$t=I=>Q.get("/dashboard/chart",{params:I?{groupId:I}:{}}),wt=()=>Q.get("/groups/list"),Mt={class:"stats-container"},Ct={class:"stat-header"},Nt={class:"stat-content"},St={class:"stat-value"},Lt={class:"stat-bar"},qt={class:"stat-content"},Dt={class:"stat-value"},Tt={class:"stat-bar"},zt={class:"stat-header"},Pt={class:"stat-content"},It={class:"stat-value"},Bt={class:"stat-bar"},Ft={class:"stat-header"},Vt={class:"stat-content"},Gt={class:"stat-value"},At={class:"stat-bar"},Yt=tt({__name:"BaseInfoCard",setup(I){const o=f(null),N=f(!0),S=f({}),Y=(p,i="count")=>i==="rate"?`${p.toFixed(2)}%`:p>=1e3?`${(p/1e3).toFixed(1)}K`:p.toString(),k=p=>`${p>=0?"+":""}${p.toFixed(1)}%`,M=async()=>{try{N.value=!0;const p=await kt();o.value=p.data,setTimeout(()=>{var i,l,v,C,B,D,E,F,X,m;S.value={key_count:(((l=(i=o.value)==null?void 0:i.key_count)==null?void 0:l.value)??0)/((((C=(v=o.value)==null?void 0:v.key_count)==null?void 0:C.value)??1)+(((D=(B=o.value)==null?void 0:B.key_count)==null?void 0:D.sub_value)??1)),group_count:1,request_count:Math.min(100+(((F=(E=o.value)==null?void 0:E.request_count)==null?void 0:F.trend)??0),100)/100,error_rate:(100-(((m=(X=o.value)==null?void 0:X.error_rate)==null?void 0:m.value)??0))/100}},0)}catch(p){console.error("获取统计数据失败:",p)}finally{N.value=!1}};return nt(()=>{M()}),(p,i)=>(c(),u("div",Mt,[h(d(J),{vertical:"",size:"medium"},{default:_(()=>[h(d(pt),{cols:4,"x-gap":20,"y-gap":20,responsive:"screen"},{default:_(()=>[h(d(U),{span:"1"},{default:_(()=>[h(d(Z),{bordered:!1,class:"stat-card",style:{"animation-delay":"0s"}},{default:_(()=>{var l,v,C;return[e("div",Ct,[i[0]||(i[0]=e("div",{class:"stat-icon key-icon"},"🔑",-1)),(l=o.value)!=null&&l.key_count.sub_value?(c(),K(d(ft),{key:0,trigger:"hover"},{trigger:_(()=>[h(d(R),{type:"error",size:"small",class:"stat-trend"},{default:_(()=>[A(y(o.value.key_count.sub_value),1)]),_:1})]),default:_(()=>[A(" "+y(o.value.key_count.sub_value_tip),1)]),_:1})):P("",!0)]),e("div",Nt,[e("div",St,y(((C=(v=o.value)==null?void 0:v.key_count)==null?void 0:C.value)??0),1),i[1]||(i[1]=e("div",{class:"stat-title"},"密钥数量",-1))]),e("div",Lt,[e("div",{class:"stat-bar-fill key-bar",style:w({width:`${(S.value.key_count??0)*100}%`})},null,4)])]}),_:1})]),_:1}),h(d(U),{span:"1"},{default:_(()=>[h(d(Z),{bordered:!1,class:"stat-card",style:{"animation-delay":"0.05s"}},{default:_(()=>{var l,v;return[i[3]||(i[3]=e("div",{class:"stat-header"},[e("div",{class:"stat-icon group-icon"},"📁")],-1)),e("div",qt,[e("div",Dt,y(((v=(l=o.value)==null?void 0:l.group_count)==null?void 0:v.value)??0),1),i[2]||(i[2]=e("div",{class:"stat-title"},"分组数量",-1))]),e("div",Tt,[e("div",{class:"stat-bar-fill group-bar",style:w({width:`${(S.value.group_count??0)*100}%`})},null,4)])]}),_:1,__:[3]})]),_:1}),h(d(U),{span:"1"},{default:_(()=>[h(d(Z),{bordered:!1,class:"stat-card",style:{"animation-delay":"0.1s"}},{default:_(()=>{var l,v;return[e("div",zt,[i[4]||(i[4]=e("div",{class:"stat-icon request-icon"},"📈",-1)),(l=o.value)!=null&&l.request_count&&o.value.request_count.trend!==void 0?(c(),K(d(R),{key:0,type:(v=o.value)!=null&&v.request_count.trend_is_growth?"success":"error",size:"small",class:"stat-trend"},{default:_(()=>[A(y(o.value?k(o.value.request_count.trend):"--"),1)]),_:1},8,["type"])):P("",!0)]),e("div",Pt,[e("div",It,y(o.value?Y(o.value.request_count.value):"--"),1),i[5]||(i[5]=e("div",{class:"stat-title"},"24小时请求",-1))]),e("div",Bt,[e("div",{class:"stat-bar-fill request-bar",style:w({width:`${(S.value.request_count??0)*100}%`})},null,4)])]}),_:1})]),_:1}),h(d(U),{span:"1"},{default:_(()=>[h(d(Z),{bordered:!1,class:"stat-card",style:{"animation-delay":"0.15s"}},{default:_(()=>{var l,v;return[e("div",Ft,[i[6]||(i[6]=e("div",{class:"stat-icon error-icon"},"🛡️",-1)),((l=o.value)==null?void 0:l.error_rate.trend)!==0?(c(),K(d(R),{key:0,type:(v=o.value)!=null&&v.error_rate.trend_is_growth?"success":"error",size:"small",class:"stat-trend"},{default:_(()=>[A(y(o.value?k(o.value.error_rate.trend):"--"),1)]),_:1},8,["type"])):P("",!0)]),e("div",Vt,[e("div",Gt,y(o.value?Y(o.value.error_rate.value??0,"rate"):"--"),1),i[7]||(i[7]=e("div",{class:"stat-title"},"24小时错误率",-1))]),e("div",At,[e("div",{class:"stat-bar-fill error-bar",style:w({width:`${(S.value.error_rate??0)*100}%`})},null,4)])]}),_:1})]),_:1})]),_:1})]),_:1})]))}}),Et=et(Yt,[["__scopeId","data-v-d6d00bf2"]]),Xt={class:"chart-container"},jt={class:"chart-header"},Ot={key:0,class:"chart-content"},Ut={class:"chart-wrapper"},Zt={class:"chart-legend"},Ht={class:"legend-label"},Kt={class:"y-axis"},Rt=["x1","y1","x2","y2"],Wt=["x1","y1","x2","y2"],Jt=["x","y"],Qt={class:"x-axis"},te=["x1","y1","x2","y2"],ee=["x1","y1","x2","y2"],se=["x","y"],ae=["id"],oe=["stop-color"],le=["stop-color"],ne=["d","fill"],re=["d","stroke","stroke-width"],ie=["cx","cy","r","fill","stroke"],ce=["x1","y1","x2","y2"],ue={class:"tooltip-time"},de={key:1,class:"chart-loading"},lt=800,q=260,ve=tt({__name:"LineChart",setup(I){const o=f(null),N=f(null),S=f(!0),Y=f(0),k=f(null),M=f(null),p=f({x:0,y:0}),i=f(),l={top:40,right:40,bottom:60,left:80},v=f([]),C=lt-l.left-l.right,B=q-l.top-l.bottom,D=W(()=>{if(!o.value)return{min:0,max:100};const a=o.value.datasets.flatMap(n=>n.data),s=Math.max(...a,0),t=Math.min(...a,0);if(s===0&&t===0)return{min:0,max:10};const r=Math.max((s-t)*.1,1);return{min:Math.max(0,t-r),max:s+r}}),E=W(()=>{const{min:a,max:s}=D.value,t=s-a,r=5,n=t/(r-1);return Array.from({length:r},(g,x)=>a+x*n)}),F=a=>new Date(a).toLocaleTimeString(void 0,{hour:"2-digit",minute:"2-digit",hour12:!1}),X=W(()=>{if(!o.value)return[];const a=o.value.labels,t=Math.ceil(a.length/8);return a.map((r,n)=>({text:F(r),index:n})).filter((r,n)=>n%t===1)}),m=a=>{if(!o.value)return 0;const s=o.value.labels.length;return s<=1?l.left+C/2:l.left+a/(s-1)*C},L=a=>{const{min:s,max:t}=D.value,r=(a-s)/(t-s);return l.top+(1-r)*B},rt=a=>{const s=[];let t=[];return a.forEach((r,n)=>{r>0?t.push({value:r,index:n}):t.length>0&&(s.push(t),t=[])}),t.length>0&&s.push(t),s},it=a=>{if(a.length===0)return"";let s=-1,t=-1;for(let n=0;n<a.length;n++)a[n]>0&&(s===-1&&(s=n),t=n);if(s===-1)return"";const r=[];for(let n=s;n<=t;n++){const g=m(n),x=L(a[n]),$=n===s?"M":"L";r.push(`${$} ${g},${x}`)}return r.join(" ")},ct=a=>{const s=rt(a),t=[],r=L(D.value.min);return s.forEach(n=>{if(n.length>0){const g=n.map(b=>({x:m(b.index),y:L(b.value)})),x=g[0],$=g[g.length-1],G=g.map(b=>`L ${b.x},${b.y}`).join(" ");t.push(`M ${x.x},${r} ${G} L ${$.x},${r} Z`)}}),t.join(" ")},st=a=>a>=1e3?`${(a/1e3).toFixed(1)}K`:Math.round(a).toString(),V=a=>a.includes("失败"),ut=f("0"),at=f("0"),dt=()=>{if(!o.value)return;const a=C+B;ut.value=`${a}`,at.value=`${a}`;let s=0;const t=r=>{s||(s=r);const n=Math.min((r-s)/1500,1);at.value=`${a*(1-n)}`,Y.value=n,n<1&&requestAnimationFrame(t)};requestAnimationFrame(t)},vt=a=>{if(!o.value||!i.value)return;const s=i.value.getBoundingClientRect(),t=800/s.width,r=260/s.height,n=(a.clientX-s.left)*t,g=(a.clientY-s.top)*r;let x=1/0,$=-1;if(o.value.labels.forEach((b,j)=>{const H=m(j),O=Math.abs(n-H);O<x&&(x=O,$=j)}),x>50){k.value=null,M.value=null;return}const G=o.value.datasets.map(b=>({label:b.label,value:b.data[$],color:b.color}));if($>=0){k.value={datasetIndex:0,pointIndex:$,x:n,y:g};const b=m($),j=G.reduce((H,O)=>H+L(O.value),0)/G.length;p.value={x:b,y:j-20},M.value={time:F(o.value.labels[$]),datasets:G}}else k.value=null,M.value=null},_t=()=>{k.value=null,M.value=null},ht=async()=>{try{const a=await wt();v.value=[{label:"全部分组",value:null},...a.data.map(s=>({label:bt(s),value:s.id||0}))]}catch(a){console.error("获取分组列表失败:",a)}},ot=async()=>{try{S.value=!0;const a=await $t(N.value||void 0);o.value=a.data,setTimeout(()=>{dt()},100)}catch(a){console.error("获取图表数据失败:",a)}finally{S.value=!1}};return yt(N,()=>{ot()}),nt(()=>{ht(),ot()}),(a,s)=>(c(),u("div",Xt,[e("div",jt,[s[1]||(s[1]=e("div",{class:"chart-title-section"},[e("h3",{class:"chart-title"},"24小时请求趋势")],-1)),h(d(mt),{value:N.value,"onUpdate:value":s[0]||(s[0]=t=>N.value=t),options:v.value,placeholder:"全部分组",size:"small",style:{width:"150px"},clearable:""},null,8,["value","options"])]),o.value?(c(),u("div",Ot,[e("div",Ut,[e("div",Zt,[(c(!0),u(T,null,z(o.value.datasets,t=>(c(),u("div",{key:t.label,class:"legend-item"},[e("div",{class:"legend-indicator",style:w({backgroundColor:t.color})},null,4),e("span",Ht,y(t.label),1)]))),128))]),(c(),u("svg",{ref_key:"chartSvg",ref:i,viewBox:"0 0 800 260",class:"chart-svg",onMousemove:vt,onMouseleave:_t},[s[2]||(s[2]=e("defs",null,[e("pattern",{id:"grid",width:"40",height:"30",patternUnits:"userSpaceOnUse"},[e("path",{d:"M 40 0 L 0 0 0 30",fill:"none",stroke:"#f0f0f0","stroke-width":"1",opacity:"0.3"})])],-1)),s[3]||(s[3]=e("rect",{width:"100%",height:"100%",fill:"url(#grid)"},null,-1)),e("g",Kt,[e("line",{x1:l.left,y1:l.top,x2:l.left,y2:q-l.bottom,stroke:"#e0e0e0","stroke-width":"2"},null,8,Rt),(c(!0),u(T,null,z(E.value,(t,r)=>(c(),u("g",{key:r},[e("line",{x1:l.left-5,y1:L(t),x2:l.left,y2:L(t),stroke:"#666","stroke-width":"1"},null,8,Wt),e("text",{x:l.left-10,y:L(t)+4,"text-anchor":"end",class:"axis-label"},y(st(t)),9,Jt)]))),128))]),e("g",Qt,[e("line",{x1:l.left,y1:q-l.bottom,x2:lt-l.right,y2:q-l.bottom,stroke:"#e0e0e0","stroke-width":"2"},null,8,te),(c(!0),u(T,null,z(X.value,(t,r)=>(c(),u("g",{key:r},[e("line",{x1:m(t.index),y1:q-l.bottom,x2:m(t.index),y2:q-l.bottom+5,stroke:"#666","stroke-width":"1"},null,8,ee),e("text",{x:m(t.index),y:q-l.bottom+18,"text-anchor":"middle",class:"axis-label"},y(t.text),9,se)]))),128))]),(c(!0),u(T,null,z(o.value.datasets,(t,r)=>(c(),u("g",{key:t.label},[e("defs",null,[e("linearGradient",{id:`gradient-${r}`,x1:"0%",y1:"0%",x2:"0%",y2:"100%"},[e("stop",{offset:"0%","stop-color":t.color,"stop-opacity":"0.3"},null,8,oe),e("stop",{offset:"100%","stop-color":t.color,"stop-opacity":"0.05"},null,8,le)],8,ae)]),e("path",{d:ct(t.data),fill:`url(#gradient-${r})`,class:"area-path",style:w({opacity:V(t.label)?.3:.6})},null,12,ne),e("path",{d:it(t.data),stroke:t.color,"stroke-width":V(t.label)?1:2,fill:"none",class:"line-path",style:w({opacity:V(t.label)?.75:1,filter:"drop-shadow(0 1px 3px rgba(0,0,0,0.1))"})},null,12,re),(c(!0),u(T,null,z(t.data,(n,g)=>{var x;return c(),u("g",{key:g},[n>0?(c(),u("circle",{key:0,cx:m(g),cy:L(n),r:V(t.label)?2:3,fill:t.color,stroke:t.color,"stroke-width":"1",class:gt(["data-point",{"point-hover":((x=k.value)==null?void 0:x.pointIndex)===g}]),style:w({opacity:V(t.label)?.8:1})},null,14,ie)):P("",!0)])}),128))]))),128)),k.value?(c(),u("line",{key:0,x1:m(k.value.pointIndex),y1:l.top,x2:m(k.value.pointIndex),y2:q-l.bottom,stroke:"#999","stroke-width":"1","stroke-dasharray":"5,5",opacity:"0.7"},null,8,ce)):P("",!0)],544)),M.value?(c(),u("div",{key:0,class:"chart-tooltip",style:w({left:p.value.x+"px",top:p.value.y+"px"})},[e("div",ue,y(M.value.time),1),(c(!0),u(T,null,z(M.value.datasets,t=>(c(),u("div",{key:t.label,class:"tooltip-value"},[e("span",{class:"tooltip-color",style:w({backgroundColor:t.color})},null,4),A(" "+y(t.label)+": "+y(st(t.value)),1)]))),128))],4)):P("",!0)])])):(c(),u("div",de,[h(d(xt),{size:"large"}),s[4]||(s[4]=e("p",null,"加载中...",-1))]))]))}}),_e=et(ve,[["__scopeId","data-v-1b3581d8"]]),he={class:"dashboard-container"},pe=tt({__name:"Dashboard",setup(I){return(o,N)=>(c(),u("div",he,[h(d(J),{vertical:"",size:"large"},{default:_(()=>[h(d(J),{vertical:"",size:"large",style:{gap:"24px"}},{default:_(()=>[h(Et),h(_e,{class:"dashboard-chart"})]),_:1})]),_:1})]))}}),me=et(pe,[["__scopeId","data-v-1fbb7b4f"]]);export{me as default};
