import{d as p,c as r,g as e,p as i,r as u,A as w,a2 as x,a as o,w as t,u as l,f as k,a3 as b,F as C,N as y,G as N,T as L,K as z,J as B,j as K,X as S,_ as V}from"./index-C-we6bTS.js";const A={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},F=p({name:"LockClosedSharp",render:function(n,s){return i(),r("svg",A,s[0]||(s[0]=[e("path",{d:"M420 192h-68v-80a96 96 0 1 0-192 0v80H92a12 12 0 0 0-12 12v280a12 12 0 0 0 12 12h328a12 12 0 0 0 12-12V204a12 12 0 0 0-12-12zm-106 0H198v-80.75a58 58 0 1 1 116 0z",fill:"currentColor"},null,-1)]))}}),G={class:"login-container"},H={class:"login-content"},I={key:0},M=p({__name:"Login",setup(v){const n=u(""),s=u(!1),g=S(),m=w(),{login:_}=x(),d=async()=>{if(!n.value){m.error("请输入授权密钥");return}s.value=!0;const c=await _(n.value);s.value=!1,c&&g.push("/")};return(c,a)=>{const f=z("n-icon");return i(),r(C,null,[e("div",G,[a[3]||(a[3]=e("div",{class:"login-background"},[e("div",{class:"login-decoration"}),e("div",{class:"login-decoration-2"})],-1)),e("div",H,[a[2]||(a[2]=e("div",{class:"login-header"},[e("h1",{class:"login-title"},"GPT Load"),e("p",{class:"login-subtitle"},"智能负载均衡管理平台")],-1)),o(l(k),{class:"login-card modern-card",bordered:!1},{header:t(()=>a[1]||(a[1]=[e("div",{class:"card-header"},[e("h2",{class:"card-title"},"欢迎回来"),e("p",{class:"card-subtitle"},"请输入您的授权密钥以继续")],-1)])),default:t(()=>[o(l(y),{vertical:"",size:"large"},{default:t(()=>[o(l(N),{value:n.value,"onUpdate:value":a[0]||(a[0]=h=>n.value=h),type:"password",size:"large",placeholder:"请输入授权密钥",class:"modern-input",onKeyup:L(d,["enter"])},{prefix:t(()=>[o(f,{component:l(F)},null,8,["component"])]),_:1},8,["value"]),o(l(B),{class:"login-btn modern-button",type:"primary",size:"large",block:"",onClick:d,loading:s.value,disabled:s.value},{default:t(()=>[s.value?K("",!0):(i(),r("span",I,"立即登录"))]),_:1},8,["loading","disabled"])]),_:1})]),_:1})])]),o(b)],64)}}}),j=V(M,[["__scopeId","data-v-e474d4a8"]]);export{j as default};
