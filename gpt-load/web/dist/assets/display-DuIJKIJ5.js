function e(t){return t?t.replace(/[_-]/g," ").replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").filter(r=>r.length>0).map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" "):""}function i(t){return t.display_name||e(t.name)}function a(t){return!t||t.length<=8?t||"":`${t.substring(0,4)}...${t.substring(t.length-4)}`}function s(t){return t?t.split(",").map(n=>a(n.trim())).join(", "):""}export{a,i as g,s as m};
