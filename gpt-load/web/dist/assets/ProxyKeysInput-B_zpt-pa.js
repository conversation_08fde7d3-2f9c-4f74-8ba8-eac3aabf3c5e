import{d as g,c as w,g as u,p as x,h as y,A as B,r as v,a as r,u as o,G as S,w as i,N as z,J as C,k as V,H as k,C as G,I as P,_ as A}from"./index-C-we6bTS.js";const j={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},H=g({name:"Copy",render:function(s,l){return x(),w("svg",j,l[0]||(l[0]=[u("path",{d:"M408 480H184a72 72 0 0 1-72-72V184a72 72 0 0 1 72-72h224a72 72 0 0 1 72 72v224a72 72 0 0 1-72 72z",fill:"currentColor"},null,-1),u("path",{d:"M160 80h235.88A72.12 72.12 0 0 0 328 32H104a72 72 0 0 0-72 72v224a72.12 72.12 0 0 0 48 67.88V160a80 80 0 0 1 80-80z",fill:"currentColor"},null,-1)]))}}),T={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},U=g({name:"Key",render:function(s,l){return x(),w("svg",T,l[0]||(l[0]=[u("path",{d:"M218.1 167.17c0 13 0 25.6 4.1 37.4c-43.1 50.6-156.9 184.3-167.5 194.5a20.17 20.17 0 0 0-6.7 15c0 8.5 5.2 16.7 9.6 21.3c6.6 6.9 34.8 33 40 28c15.4-15 18.5-19 24.8-25.2c9.5-9.3-1-28.3 2.3-36s6.8-9.2 12.5-10.4s15.8 2.9 23.7 3c8.3.1 12.8-3.4 19-9.2c5-4.6 8.6-8.9 8.7-15.6c.2-9-12.8-20.9-3.1-30.4s23.7 6.2 34 5s22.8-15.5 24.1-21.6s-11.7-21.8-9.7-30.7c.7-3 6.8-10 11.4-11s25 6.9 29.6 5.9c5.6-1.2 12.1-7.1 17.4-10.4c15.5 6.7 29.6 9.4 47.7 9.4c68.5 0 124-53.4 124-119.2S408.5 48 340 48s-121.9 53.37-121.9 119.17zM400 144a32 32 0 1 1-32-32a32 32 0 0 1 32 32z",fill:"currentColor"},null,-1)]))}});async function $(a){if(navigator.clipboard&&window.isSecureContext)try{return await navigator.clipboard.writeText(a),!0}catch(s){console.error("使用 navigator.clipboard 复制失败:",s)}try{const s=document.createElement("input");s.style.position="fixed",s.style.opacity="0",s.value=a,document.body.appendChild(s),s.select();const l=document.execCommand("copy");return document.body.removeChild(s),l?!0:(console.error("使用 execCommand 复制失败"),!1)}catch(s){return console.error("后备复制方法执行出错:",s),!1}}const W={async getSettings(){return(await y.get("/settings")).data||[]},updateSettings(a){return y.put("/settings",a)},async getChannelTypes(){return(await y.get("/channel-types")).data||[]}},E={class:"proxy-keys-input"},J=g({__name:"ProxyKeysInput",props:{modelValue:{},placeholder:{default:"多个密钥请用英文逗号 , 分隔"},size:{default:"small"}},emits:["update:modelValue"],setup(a,{emit:s}){const l=a,h=s,m=B(),f=v(!1),p=v(1),d=v(!1);function K(t){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";let n="";for(let c=0;c<t;c++)n+=e.charAt(Math.floor(Math.random()*e.length));return n}function _(){const t=[];for(let e=0;e<p.value;e++)t.push(`sk-${K(48)}`);return t}function b(){f.value=!0,p.value=1}function N(){if(!d.value)try{d.value=!0;const t=_();let n=(l.modelValue||"").trim();n&&!n.endsWith(",")&&(n+=","),n?n+=t.join(","):n=t.join(","),h("update:modelValue",n),f.value=!1,m.success(`成功生成 ${p.value} 个密钥`)}finally{d.value=!1}}async function M(){const t=l.modelValue||"";if(!t.trim()){m.warning("暂无密钥可复制");return}const e=t.split(",").map(c=>c.trim()).filter(c=>c.length>0).join(`
`);await $(e)?m.success("密钥已复制到剪贴板"):m.error("复制失败，请手动复制")}function I(t){h("update:modelValue",t)}return(t,e)=>(x(),w("div",E,[r(o(S),{value:t.modelValue,placeholder:t.placeholder,clearable:"",size:t.size,"onUpdate:value":I},{suffix:i(()=>[r(o(z),{size:4,"wrap-item":!1},{default:i(()=>[r(o(C),{text:"",type:"primary",size:t.size,onClick:b},{icon:i(()=>[r(o(k),{component:o(U)},null,8,["component"])]),default:i(()=>[e[2]||(e[2]=V(" 生成 "))]),_:1,__:[2]},8,["size"]),r(o(C),{text:"",type:"tertiary",size:t.size,onClick:M,style:{opacity:"0.7"}},{icon:i(()=>[r(o(k),{component:o(H)},null,8,["component"])]),default:i(()=>[e[3]||(e[3]=V(" 复制 "))]),_:1,__:[3]},8,["size"])]),_:1})]),_:1},8,["value","placeholder","size"]),r(o(G),{show:f.value,"onUpdate:show":e[1]||(e[1]=n=>f.value=n),preset:"dialog",title:"生成代理密钥","positive-text":"确认生成","negative-text":"取消","positive-button-props":{loading:d.value},onPositiveClick:N},{default:i(()=>[r(o(z),{vertical:"",size:16},{default:i(()=>[u("div",null,[e[4]||(e[4]=u("p",{style:{margin:"0 0 8px 0",color:"#666","font-size":"14px"}}," 请输入要生成的密钥数量（最大100个）： ",-1)),r(o(P),{value:p.value,"onUpdate:value":e[0]||(e[0]=n=>p.value=n),min:1,max:100,placeholder:"请输入数量",style:{width:"100%"},disabled:d.value},null,8,["value","disabled"])]),e[5]||(e[5]=u("div",{style:{color:"#999","font-size":"12px","line-height":"1.4"}},[u("p",null,"生成的密钥将会插入到当前输入框内容的后面，以逗号分隔")],-1))]),_:1,__:[5]})]),_:1},8,["show","positive-button-props"])]))}}),q=A(J,[["__scopeId","data-v-6aa83332"]]);export{q as P,$ as c,W as s};
