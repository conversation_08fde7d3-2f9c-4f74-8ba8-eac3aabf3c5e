time="2025-07-31 14:56:38" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 14:56:38" level=info msg="Starting as Master Node."
time="2025-07-31 14:56:38" level=info msg="Database auto-migration completed."
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: app_url = http://127.0.0.1:3001"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: request_log_retention_days = 7"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: request_log_write_interval_minutes = 1"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: proxy_keys = sk-gpt-load-2024"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: request_timeout = 600"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: connect_timeout = 15"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: idle_conn_timeout = 120"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: response_header_timeout = 600"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: max_idle_conns = 100"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: max_idle_conns_per_host = 50"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: max_retries = 3"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: blacklist_threshold = 3"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: key_validation_interval_minutes = 60"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: key_validation_concurrency = 10"
time="2025-07-31 14:56:38" level=info msg="Initialized system setting: key_validation_timeout_seconds = 20"
time="2025-07-31 14:56:38" level=info msg="System settings initialized in DB."
time="2025-07-31 14:56:38" level=info
time="2025-07-31 14:56:38" level=info msg="========= System Settings ========="
time="2025-07-31 14:56:38" level=info msg="  --- Basic Settings ---"
time="2025-07-31 14:56:38" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 14:56:38" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 14:56:38" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 14:56:38" level=info msg="  --- Request Behavior ---"
time="2025-07-31 14:56:38" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 14:56:38" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 14:56:38" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 14:56:38" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 14:56:38" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 14:56:38" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 14:56:38" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 14:56:38" level=info msg="    Max Retries: 3"
time="2025-07-31 14:56:38" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 14:56:38" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 14:56:38" level=info msg="===================================="
time="2025-07-31 14:56:38" level=info
time="2025-07-31 14:56:38" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 14:56:38" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 14:56:38" level=info
time="2025-07-31 14:56:38" level=info msg="======= Server Configuration ======="
time="2025-07-31 14:56:38" level=info msg="  --- Server ---"
time="2025-07-31 14:56:38" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 14:56:38" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 14:56:38" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 14:56:38" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 14:56:38" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 14:56:38" level=info msg="  --- Performance ---"
time="2025-07-31 14:56:38" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 14:56:38" level=info msg="  --- Security ---"
time="2025-07-31 14:56:38" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 14:56:38" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 14:56:38" level=info msg="  --- Logging ---"
time="2025-07-31 14:56:38" level=info msg="    Log Level: info"
time="2025-07-31 14:56:38" level=info msg="    Log Format: text"
time="2025-07-31 14:56:38" level=info msg="    File Logging: true"
time="2025-07-31 14:56:38" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 14:56:38" level=info msg="  --- Dependencies ---"
time="2025-07-31 14:56:38" level=info msg="    Database: configured"
time="2025-07-31 14:56:38" level=info msg="    Redis: not configured"
time="2025-07-31 14:56:38" level=info msg="===================================="
time="2025-07-31 14:56:38" level=info
time="2025-07-31 14:56:38" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 14:56:38" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 14:56:38" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 14:56:38" level=info
time="2025-07-31 14:56:38" level=fatal msg="Server startup failed: listen tcp 127.0.0.1:3001: bind: address already in use"
time="2025-07-31 14:58:23" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 14:58:23" level=info msg="Starting as Master Node."
time="2025-07-31 14:58:23" level=info msg="Database auto-migration completed."
time="2025-07-31 14:58:23" level=info msg="System settings initialized in DB."
time="2025-07-31 14:58:23" level=info
time="2025-07-31 14:58:23" level=info msg="========= System Settings ========="
time="2025-07-31 14:58:23" level=info msg="  --- Basic Settings ---"
time="2025-07-31 14:58:23" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 14:58:23" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 14:58:23" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 14:58:23" level=info msg="  --- Request Behavior ---"
time="2025-07-31 14:58:23" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 14:58:23" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 14:58:23" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 14:58:23" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 14:58:23" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 14:58:23" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 14:58:23" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 14:58:23" level=info msg="    Max Retries: 3"
time="2025-07-31 14:58:23" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 14:58:23" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 14:58:23" level=info msg="===================================="
time="2025-07-31 14:58:23" level=info
time="2025-07-31 14:58:23" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 14:58:23" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 14:58:23" level=info
time="2025-07-31 14:58:23" level=info msg="======= Server Configuration ======="
time="2025-07-31 14:58:23" level=info msg="  --- Server ---"
time="2025-07-31 14:58:23" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 14:58:23" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 14:58:23" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 14:58:23" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 14:58:23" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 14:58:23" level=info msg="  --- Performance ---"
time="2025-07-31 14:58:23" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 14:58:23" level=info msg="  --- Security ---"
time="2025-07-31 14:58:23" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 14:58:23" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 14:58:23" level=info msg="  --- Logging ---"
time="2025-07-31 14:58:23" level=info msg="    Log Level: info"
time="2025-07-31 14:58:23" level=info msg="    Log Format: text"
time="2025-07-31 14:58:23" level=info msg="    File Logging: true"
time="2025-07-31 14:58:23" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 14:58:23" level=info msg="  --- Dependencies ---"
time="2025-07-31 14:58:23" level=info msg="    Database: configured"
time="2025-07-31 14:58:23" level=info msg="    Redis: not configured"
time="2025-07-31 14:58:23" level=info msg="===================================="
time="2025-07-31 14:58:23" level=info
time="2025-07-31 14:58:23" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 14:58:23" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 14:58:23" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 14:58:23" level=info
time="2025-07-31 14:58:47" level=info msg="GET /api/dashboard/stats - 200 - 1.284365ms"
time="2025-07-31 15:04:10" level=info msg="GET / - 200 - 3.474864ms"
time="2025-07-31 15:04:10" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 617.462µs"
time="2025-07-31 15:04:10" level=info msg="GET /assets/index-C-we6bTS.js - 200 - 49.484467ms"
time="2025-07-31 15:04:11" level=info msg="GET /assets/Dashboard-DPf9KtA_.js - 200 - 805.191µs"
time="2025-07-31 15:04:11" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 393.174µs"
time="2025-07-31 15:04:11" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 162.397µs"
time="2025-07-31 15:04:12" level=warning msg="GET /api/groups/list - 401 - 98.494µs"
time="2025-07-31 15:04:12" level=warning msg="GET /api/dashboard/chart - 401 - 57.409µs"
time="2025-07-31 15:04:12" level=warning msg="GET /api/tasks/status - 401 - 37.348µs"
time="2025-07-31 15:04:12" level=warning msg="GET /api/dashboard/stats - 401 - 34.751µs"
time="2025-07-31 15:04:13" level=info msg="GET /login - 200 - 246.657µs"
time="2025-07-31 15:04:13" level=info msg="GET /login - 200 - 245.147µs"
time="2025-07-31 15:04:14" level=warning msg="GET /api/tasks/status - 401 - 53.811µs"
time="2025-07-31 15:04:18" level=info msg="GET /assets/Login-BKCd_56e.js - 200 - 371.482µs"
time="2025-07-31 15:04:18" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 316.883µs"
time="2025-07-31 15:04:21" level=warning msg="POST /api/auth/login - 401 - 215.474µs"
time="2025-07-31 15:04:32" level=warning msg="POST /api/auth/login - 401 - 121.565µs"
time="2025-07-31 15:05:45" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 15:05:45" level=info msg="Starting as Master Node."
time="2025-07-31 15:05:45" level=info msg="Database auto-migration completed."
time="2025-07-31 15:05:45" level=info msg="System settings initialized in DB."
time="2025-07-31 15:05:45" level=info
time="2025-07-31 15:05:45" level=info msg="========= System Settings ========="
time="2025-07-31 15:05:45" level=info msg="  --- Basic Settings ---"
time="2025-07-31 15:05:45" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 15:05:45" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 15:05:45" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 15:05:45" level=info msg="  --- Request Behavior ---"
time="2025-07-31 15:05:45" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 15:05:45" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 15:05:45" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 15:05:45" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 15:05:45" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 15:05:45" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 15:05:45" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 15:05:45" level=info msg="    Max Retries: 3"
time="2025-07-31 15:05:45" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 15:05:45" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 15:05:45" level=info msg="===================================="
time="2025-07-31 15:05:45" level=info
time="2025-07-31 15:05:45" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 15:05:45" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 15:05:45" level=info
time="2025-07-31 15:05:45" level=info msg="======= Server Configuration ======="
time="2025-07-31 15:05:45" level=info msg="  --- Server ---"
time="2025-07-31 15:05:45" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 15:05:45" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 15:05:45" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 15:05:45" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 15:05:45" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 15:05:45" level=info msg="  --- Performance ---"
time="2025-07-31 15:05:45" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 15:05:45" level=info msg="  --- Security ---"
time="2025-07-31 15:05:45" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 15:05:45" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 15:05:45" level=info msg="  --- Logging ---"
time="2025-07-31 15:05:45" level=info msg="    Log Level: info"
time="2025-07-31 15:05:45" level=info msg="    Log Format: text"
time="2025-07-31 15:05:45" level=info msg="    File Logging: true"
time="2025-07-31 15:05:45" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 15:05:45" level=info msg="  --- Dependencies ---"
time="2025-07-31 15:05:45" level=info msg="    Database: configured"
time="2025-07-31 15:05:45" level=info msg="    Redis: not configured"
time="2025-07-31 15:05:45" level=info msg="===================================="
time="2025-07-31 15:05:45" level=info
time="2025-07-31 15:05:45" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 15:05:45" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 15:05:45" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 15:05:45" level=info
time="2025-07-31 15:06:20" level=info msg="GET /api/dashboard/stats - 200 - 1.096419ms"
time="2025-07-31 15:06:29" level=warning msg="POST /api/auth/login - 400 - 201.697µs"
time="2025-07-31 15:06:53" level=info msg="POST /api/auth/login - 200 - 115.804µs"
time="2025-07-31 15:07:03" level=info msg="GET / - 200 - 3.985935ms"
time="2025-07-31 15:10:21" level=info msg="GET /login - 200 - 506.103µs"
time="2025-07-31 15:10:25" level=info msg="POST /api/auth/login - 200 - 109.629µs"
time="2025-07-31 15:10:25" level=info msg="GET /api/tasks/status - 200 - 170.153µs"
time="2025-07-31 15:10:25" level=info msg="GET /api/dashboard/stats - 200 - 882.01µs"
time="2025-07-31 15:10:25" level=info msg="GET /api/dashboard/chart - 200 - 729.222µs"
time="2025-07-31 15:10:25" level=info msg="GET /api/groups/list - 200 - 774.639µs"
time="2025-07-31 15:20:50" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 846.58µs"
time="2025-07-31 15:20:50" level=info msg="GET /assets/Keys-CCps4UaT.css - 200 - 889.661µs"
time="2025-07-31 15:20:50" level=info msg="GET /assets/ProxyKeysInput-B_zpt-pa.js - 200 - 2.094208ms"
time="2025-07-31 15:20:50" level=info msg="GET /assets/Search-LBeTRPTK.js - 200 - 275.483µs"
time="2025-07-31 15:20:50" level=info msg="GET /assets/Keys-BkMVgyu3.js - 200 - 2.533342ms"
time="2025-07-31 15:20:51" level=info msg="GET /api/groups/config-options - 200 - 293.629µs"
time="2025-07-31 15:20:51" level=info msg="GET /api/groups - 200 - 655.049µs"
time="2025-07-31 15:20:54" level=info msg="GET /api/channel-types - 200 - 47.132µs"
time="2025-07-31 15:20:54" level=info msg="GET /api/groups/config-options - 200 - 188.698µs"
time="2025-07-31 15:22:23" level=info msg="POST /api/groups - 200 - 7.701076ms"
time="2025-07-31 15:22:23" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 15:22:24" level=info msg="GET /api/groups - 200 - 673.568µs"
time="2025-07-31 15:22:24" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.008265ms"
time="2025-07-31 15:22:24" level=info msg="GET /api/groups/1/stats - 200 - 2.710733ms"
time="2025-07-31 15:22:30" level=info msg="POST /api/keys/add-async - 200 - 1.077705ms"
time="2025-07-31 15:22:30" level=info msg="GET /api/tasks/status - 200 - 122.835µs"
time="2025-07-31 15:22:30" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.200141ms"
time="2025-07-31 15:22:30" level=info msg="GET /api/groups/1/stats - 200 - 1.502387ms"
time="2025-07-31 15:22:36" level=info msg="POST /api/keys/test-multiple - 200 - 420.668985ms"
time="2025-07-31 15:22:37" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.939781ms"
time="2025-07-31 15:22:37" level=info msg="GET /api/groups/1/stats - 200 - 1.259775ms"
time="2025-07-31 15:22:55" level=info msg="GET /api/channel-types - 200 - 48.244µs"
time="2025-07-31 15:22:55" level=info msg="GET /api/groups/config-options - 200 - 773.641µs"
time="2025-07-31 15:23:30" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 3.253279342s"
time="2025-07-31 15:23:45" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 15:23:47" level=info msg="GET /assets/Settings-Dr4IYJ0L.js - 200 - 565.498µs"
time="2025-07-31 15:23:48" level=info msg="GET /api/settings - 200 - 301.383µs"
time="2025-07-31 15:25:09" level=info msg="GET /assets/Logs-gtS3zVqJ.js - 200 - 1.082732ms"
time="2025-07-31 15:25:09" level=info msg="GET /assets/Logs-D_3m6Xv1.css - 200 - 1.388106ms"
time="2025-07-31 15:25:10" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 712.75µs"
time="2025-07-31 15:25:10" level=info msg="GET /api/groups/config-options - 200 - 237.291µs"
time="2025-07-31 15:25:10" level=info msg="GET /api/groups - 200 - 650.265µs"
time="2025-07-31 15:25:10" level=info msg="GET /api/groups/1/stats - 200 - 1.167722ms"
time="2025-07-31 15:25:10" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.116447ms"
time="2025-07-31 15:25:11" level=info msg="GET /api/dashboard/stats - 200 - 852.271µs"
time="2025-07-31 15:25:11" level=info msg="GET /api/dashboard/chart - 200 - 371.59µs"
time="2025-07-31 15:25:11" level=info msg="GET /api/groups/list - 200 - 235.886µs"
time="2025-07-31 15:25:24" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 15:25:24" level=info msg="Starting as Master Node."
time="2025-07-31 15:25:24" level=info msg="Database auto-migration completed."
time="2025-07-31 15:25:24" level=info msg="System settings initialized in DB."
time="2025-07-31 15:25:24" level=info
time="2025-07-31 15:25:24" level=info msg="========= System Settings ========="
time="2025-07-31 15:25:24" level=info msg="  --- Basic Settings ---"
time="2025-07-31 15:25:24" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 15:25:24" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 15:25:24" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 15:25:24" level=info msg="  --- Request Behavior ---"
time="2025-07-31 15:25:24" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 15:25:24" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 15:25:24" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 15:25:24" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 15:25:24" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 15:25:24" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 15:25:24" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 15:25:24" level=info msg="    Max Retries: 3"
time="2025-07-31 15:25:24" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 15:25:24" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 15:25:24" level=info msg="===================================="
time="2025-07-31 15:25:24" level=info
time="2025-07-31 15:25:24" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 15:25:24" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 15:25:24" level=info
time="2025-07-31 15:25:24" level=info msg="======= Server Configuration ======="
time="2025-07-31 15:25:24" level=info msg="  --- Server ---"
time="2025-07-31 15:25:24" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 15:25:24" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 15:25:24" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 15:25:24" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 15:25:24" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 15:25:24" level=info msg="  --- Performance ---"
time="2025-07-31 15:25:24" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 15:25:24" level=info msg="  --- Security ---"
time="2025-07-31 15:25:24" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 15:25:24" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 15:25:24" level=info msg="  --- Logging ---"
time="2025-07-31 15:25:24" level=info msg="    Log Level: info"
time="2025-07-31 15:25:24" level=info msg="    Log Format: text"
time="2025-07-31 15:25:24" level=info msg="    File Logging: true"
time="2025-07-31 15:25:24" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 15:25:24" level=info msg="  --- Dependencies ---"
time="2025-07-31 15:25:24" level=info msg="    Database: configured"
time="2025-07-31 15:25:24" level=info msg="    Redis: not configured"
time="2025-07-31 15:25:24" level=info msg="===================================="
time="2025-07-31 15:25:24" level=info
time="2025-07-31 15:25:24" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 15:25:24" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 15:25:24" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 15:25:24" level=info
time="2025-07-31 15:25:24" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 15:25:48" level=info msg="POST /api/auth/login - 200 - 175.484µs"
time="2025-07-31 15:25:58" level=info msg="GET /api/dashboard/stats - 200 - 1.233952ms"
time="2025-07-31 16:21:36" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 4.002464541s"
time="2025-07-31 16:21:53" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 17.053906426s"
time="2025-07-31 16:22:18" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.42026409s"
time="2025-07-31 16:22:24" level=info msg="Successfully flushed 3 request logs."
time="2025-07-31 16:23:24" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 16:23:28" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 24.490040822s"
time="2025-07-31 16:23:53" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 5.742494406s"
time="2025-07-31 16:24:24" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 16:24:28" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 32.058393779s"
time="2025-07-31 16:25:24" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 16:25:24" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 16:25:31" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 28.616306042s"
time="2025-07-31 16:27:32" level=info msg="GET / - 200 - 4.441347ms"
time="2025-07-31 16:29:51" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 2.567618995s"
time="2025-07-31 16:30:24" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 16:30:30" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 35.654592707s"
time="2025-07-31 17:03:13" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 17:03:13" level=info msg="Starting as Master Node."
time="2025-07-31 17:03:13" level=info msg="Database auto-migration completed."
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: app_url = http://127.0.0.1:3001"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: request_log_retention_days = 7"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: request_log_write_interval_minutes = 1"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: proxy_keys = yu138187.."
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: request_timeout = 600"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: connect_timeout = 15"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: idle_conn_timeout = 120"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: response_header_timeout = 600"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: max_idle_conns = 100"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: max_idle_conns_per_host = 50"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: max_retries = 3"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: blacklist_threshold = 3"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: key_validation_interval_minutes = 60"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: key_validation_concurrency = 10"
time="2025-07-31 17:03:13" level=info msg="Initialized system setting: key_validation_timeout_seconds = 20"
time="2025-07-31 17:03:13" level=info msg="System settings initialized in DB."
time="2025-07-31 17:03:13" level=info
time="2025-07-31 17:03:13" level=info msg="========= System Settings ========="
time="2025-07-31 17:03:13" level=info msg="  --- Basic Settings ---"
time="2025-07-31 17:03:13" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 17:03:13" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 17:03:13" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 17:03:13" level=info msg="  --- Request Behavior ---"
time="2025-07-31 17:03:13" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 17:03:13" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 17:03:13" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 17:03:13" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 17:03:13" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 17:03:13" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 17:03:13" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 17:03:13" level=info msg="    Max Retries: 3"
time="2025-07-31 17:03:13" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 17:03:13" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 17:03:13" level=info msg="===================================="
time="2025-07-31 17:03:13" level=info
time="2025-07-31 17:03:13" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 17:03:13" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 17:03:13" level=info
time="2025-07-31 17:03:13" level=info msg="======= Server Configuration ======="
time="2025-07-31 17:03:13" level=info msg="  --- Server ---"
time="2025-07-31 17:03:13" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 17:03:13" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 17:03:13" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 17:03:13" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 17:03:13" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 17:03:13" level=info msg="  --- Performance ---"
time="2025-07-31 17:03:13" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 17:03:13" level=info msg="  --- Security ---"
time="2025-07-31 17:03:13" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 17:03:13" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 17:03:13" level=info msg="  --- Logging ---"
time="2025-07-31 17:03:13" level=info msg="    Log Level: info"
time="2025-07-31 17:03:13" level=info msg="    Log Format: text"
time="2025-07-31 17:03:13" level=info msg="    File Logging: true"
time="2025-07-31 17:03:13" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 17:03:13" level=info msg="  --- Dependencies ---"
time="2025-07-31 17:03:13" level=info msg="    Database: configured"
time="2025-07-31 17:03:13" level=info msg="    Redis: not configured"
time="2025-07-31 17:03:13" level=info msg="===================================="
time="2025-07-31 17:03:13" level=info
time="2025-07-31 17:03:13" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 17:03:13" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 17:03:13" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 17:03:13" level=info
time="2025-07-31 17:03:16" level=info msg="GET / - 200 - 4.736893ms"
time="2025-07-31 17:03:23" level=info msg="GET / - 200 - 349.412µs"
time="2025-07-31 17:03:24" level=warning msg="GET /api/dashboard/chart - 401 - 117.999µs"
time="2025-07-31 17:03:24" level=warning msg="GET /api/groups/list - 401 - 62.857µs"
time="2025-07-31 17:03:24" level=warning msg="GET /api/dashboard/stats - 401 - 46.146µs"
time="2025-07-31 17:03:24" level=warning msg="GET /api/tasks/status - 401 - 37.869µs"
time="2025-07-31 17:03:25" level=info msg="GET /login - 200 - 1.339013ms"
time="2025-07-31 17:03:27" level=warning msg="POST /api/auth/login - 401 - 321.084µs"
time="2025-07-31 17:04:22" level=info msg="GET / - 200 - 115.964µs"
time="2025-07-31 17:05:07" level=info msg="Shutting down server..."
time="2025-07-31 17:05:07" level=info msg="HTTP server has been shut down."
time="2025-07-31 17:05:07" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 17:05:07" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 17:05:07" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 17:05:07" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 17:05:07" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 17:05:07" level=info msg="All background services stopped."
time="2025-07-31 17:05:07" level=info msg="Server exited gracefully"
time="2025-07-31 17:05:09" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 17:05:09" level=info msg="Starting as Master Node."
time="2025-07-31 17:05:09" level=info msg="Database auto-migration completed."
time="2025-07-31 17:05:09" level=info msg="System settings initialized in DB."
time="2025-07-31 17:05:09" level=info
time="2025-07-31 17:05:09" level=info msg="========= System Settings ========="
time="2025-07-31 17:05:09" level=info msg="  --- Basic Settings ---"
time="2025-07-31 17:05:09" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 17:05:09" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 17:05:09" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 17:05:09" level=info msg="  --- Request Behavior ---"
time="2025-07-31 17:05:09" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 17:05:09" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 17:05:09" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 17:05:09" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 17:05:09" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 17:05:09" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 17:05:09" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 17:05:09" level=info msg="    Max Retries: 3"
time="2025-07-31 17:05:09" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 17:05:09" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 17:05:09" level=info msg="===================================="
time="2025-07-31 17:05:09" level=info
time="2025-07-31 17:05:09" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 17:05:09" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 17:05:09" level=info
time="2025-07-31 17:05:09" level=info msg="======= Server Configuration ======="
time="2025-07-31 17:05:09" level=info msg="  --- Server ---"
time="2025-07-31 17:05:09" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 17:05:09" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 17:05:09" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 17:05:09" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 17:05:09" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 17:05:09" level=info msg="  --- Performance ---"
time="2025-07-31 17:05:09" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 17:05:09" level=info msg="  --- Security ---"
time="2025-07-31 17:05:09" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 17:05:09" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 17:05:09" level=info msg="  --- Logging ---"
time="2025-07-31 17:05:09" level=info msg="    Log Level: info"
time="2025-07-31 17:05:09" level=info msg="    Log Format: text"
time="2025-07-31 17:05:09" level=info msg="    File Logging: true"
time="2025-07-31 17:05:09" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 17:05:09" level=info msg="  --- Dependencies ---"
time="2025-07-31 17:05:09" level=info msg="    Database: configured"
time="2025-07-31 17:05:09" level=info msg="    Redis: not configured"
time="2025-07-31 17:05:09" level=info msg="===================================="
time="2025-07-31 17:05:09" level=info
time="2025-07-31 17:05:09" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 17:05:09" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 17:05:09" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 17:05:09" level=info
time="2025-07-31 17:05:12" level=info msg="GET / - 200 - 16.959858ms"
time="2025-07-31 17:05:16" level=info msg="POST /api/auth/login - 200 - 234.89µs"
time="2025-07-31 17:05:17" level=info msg="GET /api/dashboard/chart - 200 - 645.597µs"
time="2025-07-31 17:05:17" level=info msg="GET /api/tasks/status - 200 - 107.889µs"
time="2025-07-31 17:05:17" level=info msg="GET /api/dashboard/stats - 200 - 1.032663ms"
time="2025-07-31 17:05:17" level=info msg="GET /api/groups/list - 200 - 372.039µs"
time="2025-07-31 17:05:21" level=info msg="GET / - 200 - 989.733µs"
time="2025-07-31 17:05:22" level=info msg="GET /api/dashboard/stats - 200 - 752.987µs"
time="2025-07-31 17:05:22" level=info msg="GET /api/groups/list - 200 - 662.136µs"
time="2025-07-31 17:05:22" level=info msg="GET /api/dashboard/chart - 200 - 1.066784ms"
time="2025-07-31 17:05:22" level=info msg="GET /api/tasks/status - 200 - 68.289µs"
time="2025-07-31 17:07:03" level=info msg="Shutting down server..."
time="2025-07-31 17:07:03" level=info msg="HTTP server has been shut down."
time="2025-07-31 17:07:03" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 17:07:03" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 17:07:03" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 17:07:03" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 17:07:03" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 17:07:03" level=info msg="All background services stopped."
time="2025-07-31 17:07:03" level=info msg="Server exited gracefully"
time="2025-07-31 17:07:05" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 17:07:05" level=info msg="Starting as Master Node."
time="2025-07-31 17:07:05" level=info msg="Database auto-migration completed."
time="2025-07-31 17:07:05" level=info msg="System settings initialized in DB."
time="2025-07-31 17:07:05" level=info
time="2025-07-31 17:07:05" level=info msg="========= System Settings ========="
time="2025-07-31 17:07:05" level=info msg="  --- Basic Settings ---"
time="2025-07-31 17:07:05" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 17:07:05" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 17:07:05" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 17:07:05" level=info msg="  --- Request Behavior ---"
time="2025-07-31 17:07:05" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 17:07:05" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 17:07:05" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 17:07:05" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 17:07:05" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 17:07:05" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 17:07:05" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 17:07:05" level=info msg="    Max Retries: 3"
time="2025-07-31 17:07:05" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 17:07:05" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 17:07:05" level=info msg="===================================="
time="2025-07-31 17:07:05" level=info
time="2025-07-31 17:07:05" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 17:07:05" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 17:07:05" level=info
time="2025-07-31 17:07:05" level=info msg="======= Server Configuration ======="
time="2025-07-31 17:07:05" level=info msg="  --- Server ---"
time="2025-07-31 17:07:05" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 17:07:05" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 17:07:05" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 17:07:05" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 17:07:05" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 17:07:05" level=info msg="  --- Performance ---"
time="2025-07-31 17:07:05" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 17:07:05" level=info msg="  --- Security ---"
time="2025-07-31 17:07:05" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 17:07:05" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 17:07:05" level=info msg="  --- Logging ---"
time="2025-07-31 17:07:05" level=info msg="    Log Level: info"
time="2025-07-31 17:07:05" level=info msg="    Log Format: text"
time="2025-07-31 17:07:05" level=info msg="    File Logging: true"
time="2025-07-31 17:07:05" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 17:07:05" level=info msg="  --- Dependencies ---"
time="2025-07-31 17:07:05" level=info msg="    Database: configured"
time="2025-07-31 17:07:05" level=info msg="    Redis: not configured"
time="2025-07-31 17:07:05" level=info msg="===================================="
time="2025-07-31 17:07:05" level=info
time="2025-07-31 17:07:05" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 17:07:05" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 17:07:05" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 17:07:05" level=info
time="2025-07-31 17:07:33" level=warning msg="POST /api/auth/login - 401 - 175.686µs"
time="2025-07-31 17:08:10" level=info msg="Shutting down server..."
time="2025-07-31 17:08:10" level=info msg="HTTP server has been shut down."
time="2025-07-31 17:08:10" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 17:08:10" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 17:08:10" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 17:08:10" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 17:08:10" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 17:08:10" level=info msg="All background services stopped."
time="2025-07-31 17:08:10" level=info msg="Server exited gracefully"
time="2025-07-31 17:08:12" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 17:08:12" level=info msg="Starting as Master Node."
time="2025-07-31 17:08:12" level=info msg="Database auto-migration completed."
time="2025-07-31 17:08:12" level=info msg="System settings initialized in DB."
time="2025-07-31 17:08:12" level=info
time="2025-07-31 17:08:12" level=info msg="========= System Settings ========="
time="2025-07-31 17:08:12" level=info msg="  --- Basic Settings ---"
time="2025-07-31 17:08:12" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 17:08:12" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 17:08:12" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 17:08:12" level=info msg="  --- Request Behavior ---"
time="2025-07-31 17:08:12" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 17:08:12" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 17:08:12" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 17:08:12" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 17:08:12" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 17:08:12" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 17:08:12" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 17:08:12" level=info msg="    Max Retries: 3"
time="2025-07-31 17:08:12" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 17:08:12" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 17:08:12" level=info msg="===================================="
time="2025-07-31 17:08:12" level=info
time="2025-07-31 17:08:12" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 17:08:12" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 17:08:12" level=info
time="2025-07-31 17:08:12" level=info msg="======= Server Configuration ======="
time="2025-07-31 17:08:12" level=info msg="  --- Server ---"
time="2025-07-31 17:08:12" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 17:08:12" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 17:08:12" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 17:08:12" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 17:08:12" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 17:08:12" level=info msg="  --- Performance ---"
time="2025-07-31 17:08:12" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 17:08:12" level=info msg="  --- Security ---"
time="2025-07-31 17:08:12" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 17:08:12" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 17:08:12" level=info msg="  --- Logging ---"
time="2025-07-31 17:08:12" level=info msg="    Log Level: info"
time="2025-07-31 17:08:12" level=info msg="    Log Format: text"
time="2025-07-31 17:08:12" level=info msg="    File Logging: true"
time="2025-07-31 17:08:12" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 17:08:12" level=info msg="  --- Dependencies ---"
time="2025-07-31 17:08:12" level=info msg="    Database: configured"
time="2025-07-31 17:08:12" level=info msg="    Redis: not configured"
time="2025-07-31 17:08:12" level=info msg="===================================="
time="2025-07-31 17:08:12" level=info
time="2025-07-31 17:08:12" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 17:08:12" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 17:08:12" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 17:08:12" level=info
time="2025-07-31 17:08:15" level=info msg="GET / - 200 - 5.233338ms"
time="2025-07-31 17:08:26" level=info msg="POST /api/auth/login - 200 - 224.212µs"
time="2025-07-31 17:10:17" level=info msg="GET /api/dashboard/stats - 200 - 1.930158ms"
time="2025-07-31 17:16:55" level=info msg="POST /api/auth/login - 200 - 124.812µs"
time="2025-07-31 17:28:12" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 17:29:06" level=info msg="Shutting down server..."
time="2025-07-31 17:29:06" level=info msg="HTTP server has been shut down."
time="2025-07-31 17:29:06" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 17:29:06" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 17:29:06" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 17:29:06" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 17:29:06" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 17:29:06" level=info msg="All background services stopped."
time="2025-07-31 17:29:06" level=info msg="Server exited gracefully"
time="2025-07-31 17:29:08" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 17:29:08" level=info msg="Starting as Master Node."
time="2025-07-31 17:29:08" level=info msg="Database auto-migration completed."
time="2025-07-31 17:29:08" level=info msg="System settings initialized in DB."
time="2025-07-31 17:29:08" level=info
time="2025-07-31 17:29:08" level=info msg="========= System Settings ========="
time="2025-07-31 17:29:08" level=info msg="  --- Basic Settings ---"
time="2025-07-31 17:29:08" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-07-31 17:29:08" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 17:29:08" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 17:29:08" level=info msg="  --- Request Behavior ---"
time="2025-07-31 17:29:08" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 17:29:08" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 17:29:08" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 17:29:08" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 17:29:08" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 17:29:08" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 17:29:08" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 17:29:08" level=info msg="    Max Retries: 3"
time="2025-07-31 17:29:08" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 17:29:08" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 17:29:08" level=info msg="===================================="
time="2025-07-31 17:29:08" level=info
time="2025-07-31 17:29:08" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 17:29:08" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 17:29:08" level=info
time="2025-07-31 17:29:08" level=info msg="======= Server Configuration ======="
time="2025-07-31 17:29:08" level=info msg="  --- Server ---"
time="2025-07-31 17:29:08" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-07-31 17:29:08" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 17:29:08" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 17:29:08" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 17:29:08" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 17:29:08" level=info msg="  --- Performance ---"
time="2025-07-31 17:29:08" level=info msg="    Max Concurrent Requests: 200"
time="2025-07-31 17:29:08" level=info msg="  --- Security ---"
time="2025-07-31 17:29:08" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 17:29:08" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-07-31 17:29:08" level=info msg="  --- Logging ---"
time="2025-07-31 17:29:08" level=info msg="    Log Level: info"
time="2025-07-31 17:29:08" level=info msg="    Log Format: text"
time="2025-07-31 17:29:08" level=info msg="    File Logging: true"
time="2025-07-31 17:29:08" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 17:29:08" level=info msg="  --- Dependencies ---"
time="2025-07-31 17:29:08" level=info msg="    Database: configured"
time="2025-07-31 17:29:08" level=info msg="    Redis: not configured"
time="2025-07-31 17:29:08" level=info msg="===================================="
time="2025-07-31 17:29:08" level=info
time="2025-07-31 17:29:08" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 17:29:08" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-07-31 17:29:08" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-07-31 17:29:08" level=info
time="2025-07-31 17:29:11" level=info msg="GET / - 200 - 2.960281ms"
time="2025-07-31 17:29:19" level=info msg="GET / - 200 - 695.948µs"
time="2025-07-31 17:29:20" level=info msg="GET /api/tasks/status - 200 - 168.166µs"
time="2025-07-31 17:29:20" level=info msg="GET /api/dashboard/stats - 200 - 1.199479ms"
time="2025-07-31 17:29:21" level=info msg="GET /api/groups/list - 200 - 768.147µs"
time="2025-07-31 17:29:21" level=info msg="GET /api/dashboard/chart - 200 - 770.622µs"
time="2025-07-31 17:30:04" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.182053398s"
time="2025-07-31 17:30:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 18:29:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 18:59:45" level=info msg="GET /api/groups/config-options - 200 - 358.377µs"
time="2025-07-31 18:59:50" level=info msg="GET /api/groups - 200 - 954.727µs"
time="2025-07-31 18:59:53" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.599306ms"
time="2025-07-31 18:59:53" level=info msg="GET /api/groups/1/stats - 200 - 1.700268ms"
time="2025-07-31 19:00:00" level=info msg="POST /api/keys/add-async - 200 - 969.825µs"
time="2025-07-31 19:00:03" level=info msg="GET /api/tasks/status - 200 - 156.836µs"
time="2025-07-31 19:00:04" level=info msg="GET /api/groups/1/stats - 200 - 1.756293ms"
time="2025-07-31 19:00:04" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.518275ms"
time="2025-07-31 19:20:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:20:33" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 36.538895292s"
time="2025-07-31 19:22:00" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 17.867957651s"
time="2025-07-31 19:22:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:30:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:30:12" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 36.457928578s"
time="2025-07-31 19:32:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:32:12" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 33.295838702s"
time="2025-07-31 19:34:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 19:36:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:36:15" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 34.937442403s"
time="2025-07-31 19:37:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:37:33" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 35.456799745s"
time="2025-07-31 19:40:34" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 11.863495872s"
time="2025-07-31 19:41:08" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 19:41:14" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 21.266809805s"
time="2025-07-31 19:43:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:43:33" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 39.349264599s"
time="2025-07-31 19:48:50" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 15.512647999s"
time="2025-07-31 19:49:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:52:57" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 17.327595751s"
time="2025-07-31 19:53:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 19:54:00" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 19.313288049s"
time="2025-07-31 19:54:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 20:06:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 20:06:17" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 22.11483376s"
time="2025-07-31 20:08:40" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 18.13876418s"
time="2025-07-31 20:08:49" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 28.069260111s"
time="2025-07-31 20:09:08" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 20:11:21" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 6.289820852s"
time="2025-07-31 20:11:34" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 27.423396877s"
time="2025-07-31 20:12:08" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 20:17:22" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 7.715186571s"
time="2025-07-31 20:18:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 20:19:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 20:19:22" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 18.379096635s"
time="2025-07-31 20:39:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 20:57:32" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 13.743299566s"
time="2025-07-31 20:57:35" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 1.737494277s"
time="2025-07-31 20:58:08" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 20:59:06" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 31.453837672s"
time="2025-07-31 20:59:08" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 21:44:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 21:46:59" level=info msg="GET / - 200 - 59.932961ms"
time="2025-07-31 22:49:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 23:54:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 00:51:33" level=info msg="GET /sitemap.xml - 200 - 12.509475ms"
time="2025-08-01 00:51:33" level=info msg="GET /favicon.ico - 200 - 12.509891ms"
time="2025-08-01 00:51:33" level=info msg="GET / - 200 - 1.440563ms"
time="2025-08-01 00:51:34" level=info msg="GET /sitemap.xml - 200 - 249.393µs"
time="2025-08-01 00:51:34" level=info msg="GET /robots.txt - 200 - 403.521µs"
time="2025-08-01 00:51:37" level=info msg="GET / - 200 - 246.833µs"
time="2025-08-01 00:51:38" level=info msg="GET /sitemap.xml - 200 - 235.916µs"
time="2025-08-01 00:51:38" level=info msg="GET /robots.txt - 200 - 258.231µs"
time="2025-08-01 00:54:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 01:59:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 02:43:16" level=info msg="GET / - 200 - 458.239µs"
time="2025-08-01 02:43:38" level=info msg="GET /sitemap.xml - 200 - 267.506µs"
time="2025-08-01 03:04:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 04:09:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 05:09:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 06:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 07:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 08:19:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 08:57:15" level=info msg="GET / - 200 - 450.283µs"
time="2025-08-01 09:21:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 09:21:34" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 43.58024719s"
time="2025-08-01 09:24:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 09:25:23" level=info msg="GET /.env - 200 - 1.891649ms"
time="2025-08-01 09:25:23" level=info msg="GET /.env.local - 200 - 448.325µs"
time="2025-08-01 09:25:23" level=info msg="GET /.env.dev - 200 - 240.991µs"
time="2025-08-01 09:25:23" level=info msg="GET /.env.development - 200 - 240.994µs"
time="2025-08-01 09:25:28" level=info msg="GET /.env.prod - 200 - 263.077µs"
time="2025-08-01 09:25:28" level=info msg="GET /.env.production - 200 - 322.456µs"
time="2025-08-01 09:25:28" level=info msg="GET /.env.stage - 200 - 238.167µs"
time="2025-08-01 09:25:33" level=info msg="GET /.env.test - 200 - 231.85µs"
time="2025-08-01 09:25:33" level=info msg="GET /.env.example - 200 - 364.694µs"
time="2025-08-01 09:25:33" level=info msg="GET /.env.bak - 200 - 267.018µs"
time="2025-08-01 09:25:34" level=info msg="GET /.env.old - 200 - 274.297µs"
time="2025-08-01 09:25:34" level=info msg="GET /.env.testing - 200 - 271.763µs"
time="2025-08-01 09:25:34" level=info msg="GET /.env.*.local - 200 - 247.025µs"
time="2025-08-01 09:25:34" level=info msg="GET /config/.env - 200 - 242.007µs"
time="2025-08-01 09:25:34" level=info msg="GET /config/config.env - 200 - 241.764µs"
time="2025-08-01 09:25:34" level=info msg="GET /app/.env - 200 - 243.623µs"
time="2025-08-01 09:25:34" level=info msg="GET /admin/.env - 200 - 236.8µs"
time="2025-08-01 09:25:35" level=warning msg="GET /api/.env - 404 - 206.858µs"
time="2025-08-01 09:25:35" level=info msg="GET /apps/.env - 200 - 264.535µs"
time="2025-08-01 09:25:35" level=info msg="GET /server/.env - 200 - 244.87µs"
time="2025-08-01 09:25:35" level=info msg="GET /backend/.env - 200 - 238.688µs"
time="2025-08-01 09:25:35" level=info msg="GET /aws/credentials - 200 - 241.86µs"
time="2025-08-01 09:25:35" level=info msg="GET /.aws/credentials - 200 - 243.603µs"
time="2025-08-01 09:25:36" level=info msg="GET /.aws/config - 200 - 231.455µs"
time="2025-08-01 09:25:36" level=info msg="GET /config/aws.yml - 200 - 222.977µs"
time="2025-08-01 09:25:36" level=info msg="GET /config/aws.json - 200 - 264.942µs"
time="2025-08-01 09:25:36" level=info msg="GET /secrets.json - 200 - 237.817µs"
time="2025-08-01 09:25:36" level=info msg="GET /secrets.yml - 200 - 238.417µs"
time="2025-08-01 09:25:36" level=info msg="GET /credentials.json - 200 - 258.344µs"
time="2025-08-01 09:25:41" level=info msg="GET /php.ini - 200 - 233.756µs"
time="2025-08-01 09:25:47" level=info msg="GET /.user.ini - 200 - 237.81µs"
time="2025-08-01 09:25:47" level=info msg="GET /.php.ini - 200 - 232.772µs"
time="2025-08-01 09:25:47" level=info msg="GET /.htaccess - 200 - 248.965µs"
time="2025-08-01 09:25:47" level=info msg="GET /web.config - 200 - 242.422µs"
time="2025-08-01 09:25:47" level=info msg="GET /config.php - 200 - 224.136µs"
time="2025-08-01 09:25:48" level=info msg="GET /config/config.php - 200 - 248.774µs"
time="2025-08-01 09:25:48" level=info msg="GET /config/database.php - 200 - 255.312µs"
time="2025-08-01 09:25:48" level=info msg="GET /config/settings.php - 200 - 250.97µs"
time="2025-08-01 09:25:48" level=info msg="GET /app/config.php - 200 - 246.812µs"
time="2025-08-01 09:25:48" level=info msg="GET /app/config/config.php - 200 - 272.76µs"
time="2025-08-01 09:25:48" level=info msg="GET /includes/config.php - 200 - 244.106µs"
time="2025-08-01 09:25:48" level=info msg="GET /inc/config.php - 200 - 238.536µs"
time="2025-08-01 09:25:49" level=info msg="GET /sites/default/settings.php - 200 - 236.319µs"
time="2025-08-01 09:25:54" level=info msg="GET /wp-config-sample.php - 200 - 313.79µs"
time="2025-08-01 09:25:54" level=info msg="GET /configuration.php - 200 - 285.432µs"
time="2025-08-01 09:25:54" level=info msg="GET /local-config.php - 200 - 272.664µs"
time="2025-08-01 09:25:54" level=info msg="GET /db.php - 200 - 266.15µs"
time="2025-08-01 09:25:55" level=info msg="GET /application/config/database.php - 200 - 238.246µs"
time="2025-08-01 09:25:55" level=info msg="GET /application/config/config.php - 200 - 234.815µs"
time="2025-08-01 09:25:59" level=info msg="GET /var/www/html/phpinfo.php - 200 - 235.353µs"
time="2025-08-01 09:25:59" level=info msg="GET /var/www/html/info.php - 200 - 236.353µs"
time="2025-08-01 09:25:59" level=info msg="GET /docker-compose.yml - 200 - 237.646µs"
time="2025-08-01 09:26:00" level=info msg="GET /docker-compose.override.yml - 200 - 232.949µs"
time="2025-08-01 09:26:04" level=info msg="GET /docker-compose.prod.yml - 200 - 346.329µs"
time="2025-08-01 09:26:04" level=info msg="GET /docker-compose.dev.yml - 200 - 264.383µs"
time="2025-08-01 09:26:04" level=info msg="GET /Dockerfile - 200 - 265.9µs"
time="2025-08-01 09:26:05" level=info msg="GET /.dockerignore - 200 - 244.329µs"
time="2025-08-01 09:26:09" level=info msg="GET /Procfile - 200 - 250.019µs"
time="2025-08-01 09:26:10" level=info msg="GET /storage/logs/laravel.log - 200 - 247.828µs"
time="2025-08-01 09:26:10" level=info msg="GET /storage/logs/error.log - 200 - 234.742µs"
time="2025-08-01 09:26:10" level=info msg="GET /logs/debug.log - 200 - 261.869µs"
time="2025-08-01 09:26:10" level=info msg="GET /logs/app.log - 200 - 244.596µs"
time="2025-08-01 09:26:10" level=info msg="GET /debug.log - 200 - 323.893µs"
time="2025-08-01 09:26:10" level=info msg="GET /error.log - 200 - 242.719µs"
time="2025-08-01 09:26:10" level=info msg="GET /error_log - 200 - 247.5µs"
time="2025-08-01 09:26:11" level=info msg="GET /php_error_log - 200 - 233.886µs"
time="2025-08-01 09:26:11" level=info msg="GET /var/log/apache2/error.log - 200 - 245.837µs"
time="2025-08-01 09:26:11" level=info msg="GET /var/log/nginx/error.log - 200 - 231.228µs"
time="2025-08-01 09:26:11" level=info msg="GET /var/log/php_errors.log - 200 - 260.825µs"
time="2025-08-01 09:26:11" level=info msg="GET /var/log/httpd/error_log - 200 - 256.338µs"
time="2025-08-01 09:26:12" level=info msg="GET /tmp/php.log - 200 - 225.674µs"
time="2025-08-01 09:26:12" level=info msg="GET /tmp/errors.log - 200 - 244.23µs"
time="2025-08-01 09:26:12" level=info msg="GET /storage/framework/cache/ - 200 - 519.818µs"
time="2025-08-01 09:26:12" level=info msg="GET /storage/framework/views/ - 200 - 229.243µs"
time="2025-08-01 09:26:12" level=info msg="GET /storage/framework/sessions/ - 200 - 358.636µs"
time="2025-08-01 09:26:12" level=info msg="GET /db.sql - 200 - 281.616µs"
time="2025-08-01 09:26:18" level=info msg="GET /dump.sql - 200 - 230.364µs"
time="2025-08-01 09:26:18" level=info msg="GET /database.sql - 200 - 267.803µs"
time="2025-08-01 09:26:18" level=info msg="GET /backup.sql - 200 - 245.206µs"
time="2025-08-01 09:26:18" level=info msg="GET /backup_*.sql - 200 - 234.046µs"
time="2025-08-01 09:26:18" level=info msg="GET /database/database.sqlite - 200 - 241.905µs"
time="2025-08-01 09:26:18" level=info msg="GET /database/seeders/ - 200 - 236.604µs"
time="2025-08-01 09:26:19" level=info msg="GET /database/migrations/ - 200 - 259.78µs"
time="2025-08-01 09:26:19" level=info msg="GET /backup.zip - 200 - 248.701µs"
time="2025-08-01 09:26:19" level=info msg="GET /.backup - 200 - 341.485µs"
time="2025-08-01 09:26:19" level=info msg="GET /backup.tar.gz - 200 - 236.75µs"
time="2025-08-01 09:26:20" level=info msg="GET /*.bak - 200 - 299.282µs"
time="2025-08-01 09:26:20" level=info msg="GET /*.backup - 200 - 228.688µs"
time="2025-08-01 09:26:20" level=info msg="GET /*.old - 200 - 265.373µs"
time="2025-08-01 09:26:20" level=info msg="GET /*.tmp - 200 - 234.006µs"
time="2025-08-01 09:26:20" level=info msg="GET /.git-credentials - 200 - 249.906µs"
time="2025-08-01 09:26:20" level=info msg="GET /.git/config - 200 - 278.281µs"
time="2025-08-01 09:26:21" level=info msg="GET /.gitignore - 200 - 268.698µs"
time="2025-08-01 09:26:21" level=info msg="GET /.gitlab-ci.yml - 200 - 242.165µs"
time="2025-08-01 09:26:21" level=info msg="GET /.github/workflows/ - 200 - 329.885µs"
time="2025-08-01 09:26:21" level=info msg="GET /composer.json - 200 - 239.902µs"
time="2025-08-01 09:26:22" level=info msg="GET /composer.lock - 200 - 263.217µs"
time="2025-08-01 09:26:22" level=info msg="GET /package.json - 200 - 244.585µs"
time="2025-08-01 09:26:22" level=info msg="GET /package-lock.json - 200 - 269.51µs"
time="2025-08-01 09:26:22" level=info msg="GET /yarn.lock - 200 - 261.811µs"
time="2025-08-01 09:26:22" level=info msg="GET /Gemfile - 200 - 239.62µs"
time="2025-08-01 09:26:22" level=info msg="GET /Gemfile.lock - 200 - 238.267µs"
time="2025-08-01 09:26:22" level=info msg="GET /pom.xml - 200 - 282.04µs"
time="2025-08-01 09:26:22" level=info msg="GET /build.gradle - 200 - 271.525µs"
time="2025-08-01 09:26:23" level=info msg="GET /settings.gradle - 200 - 293.136µs"
time="2025-08-01 09:26:23" level=info msg="GET /requirements.txt - 200 - 241.218µs"
time="2025-08-01 09:26:23" level=info msg="GET /Pipfile - 200 - 241.851µs"
time="2025-08-01 09:26:23" level=info msg="GET /Pipfile.lock - 200 - 230.893µs"
time="2025-08-01 09:26:23" level=info msg="GET /.idea/workspace.xml - 200 - 241.463µs"
time="2025-08-01 09:26:23" level=info msg="GET /.vscode/settings.json - 200 - 254.212µs"
time="2025-08-01 09:26:23" level=info msg="GET /.project - 200 - 243.003µs"
time="2025-08-01 09:26:24" level=info msg="GET /.settings/ - 200 - 344.754µs"
time="2025-08-01 09:26:24" level=info msg="GET /.classpath - 200 - 236.144µs"
time="2025-08-01 09:26:24" level=info msg="GET /.htpasswd - 200 - 248.901µs"
time="2025-08-01 09:26:24" level=info msg="GET /.npmrc - 200 - 278.002µs"
time="2025-08-01 09:26:24" level=info msg="GET /.yarnrc - 200 - 232.531µs"
time="2025-08-01 09:26:24" level=info msg="GET /.DS_Store - 200 - 279.862µs"
time="2025-08-01 09:26:30" level=info msg="GET /config.json - 200 - 261.233µs"
time="2025-08-01 09:26:30" level=info msg="GET /config.yaml - 200 - 302.724µs"
time="2025-08-01 09:26:35" level=info msg="GET /config.yml - 200 - 262.384µs"
time="2025-08-01 09:26:36" level=info msg="GET /config/config.json - 200 - 226.992µs"
time="2025-08-01 09:32:37" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 1.252290853s"
time="2025-08-01 09:33:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 09:36:50" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 1.552089725s"
time="2025-08-01 09:37:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 09:44:19" level=info msg="GET /.git/config - 200 - 474.254µs"
time="2025-08-01 10:29:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 11:13:54" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 3.132883857s"
time="2025-08-01 11:14:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:15:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:15:28" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 50.356237045s"
time="2025-08-01 11:16:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:16:43" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.698170832s"
time="2025-08-01 11:30:46" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 12.879687654s"
time="2025-08-01 11:31:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:31:41" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 15.72805984s"
time="2025-08-01 11:32:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:33:53" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 36.621235237s"
time="2025-08-01 11:34:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:34:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 11:36:02" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 32.876533958s"
time="2025-08-01 11:36:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:37:28" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 13.663190116s"
time="2025-08-01 11:38:01" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 28.694690268s"
time="2025-08-01 11:38:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 11:39:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:39:23" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 42.925831458s"
time="2025-08-01 11:41:07" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 44.007308819s"
time="2025-08-01 11:41:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 11:44:59" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 35.42014066s"
time="2025-08-01 11:45:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 12:34:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 13:34:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 14:24:42" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 17.909641249s"
time="2025-08-01 14:25:00" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 17.113735847s"
time="2025-08-01 14:25:08" level=info msg="Successfully flushed 3 request logs."
time="2025-08-01 14:25:34" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 49.731459754s"
time="2025-08-01 14:32:08" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 45.890376362s"
time="2025-08-01 14:32:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 14:33:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 14:33:31" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 41.779074044s"
time="2025-08-01 14:34:46" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 22.503745318s"
time="2025-08-01 14:35:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 14:39:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 14:46:22" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 9.04095143s"
time="2025-08-01 14:47:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 14:47:19" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 20.619632249s"
time="2025-08-01 15:05:39" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 3.573563291s"
time="2025-08-01 15:06:07" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 26.929167342s"
time="2025-08-01 15:06:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 15:17:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 15:17:30" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 1m2.356564508s"
time="2025-08-01 15:44:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 16:49:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 16:53:41" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 29.120954172s"
time="2025-08-01 16:54:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 16:56:07" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 38.868386078s"
time="2025-08-01 16:56:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:00:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:00:15" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 30.47213591s"
time="2025-08-01 17:02:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:02:40" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 1m17.875405301s"
time="2025-08-01 17:02:43" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.85785725s"
time="2025-08-01 17:03:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:07:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:07:10" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.237388948s"
time="2025-08-01 17:08:02" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.439272409s"
time="2025-08-01 17:08:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:13:56" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 44.119149064s"
time="2025-08-01 17:14:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:16:42" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 26.660647539s"
time="2025-08-01 17:16:45" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.130069622s"
time="2025-08-01 17:17:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 17:21:54" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 35.62456458s"
time="2025-08-01 17:21:56" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 31.297081115s"
time="2025-08-01 17:22:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 17:24:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:24:10" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 24.709786963s"
time="2025-08-01 17:26:30" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 21.915701686s"
time="2025-08-01 17:27:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-01 17:27:17" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 22.853658497s"
time="2025-08-01 17:29:34" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 24.596414192s"
time="2025-08-01 17:30:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:30:41" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 30.924003907s"
time="2025-08-01 17:31:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:35:57" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 26.652230407s"
time="2025-08-01 17:36:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-01 17:49:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 18:54:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 19:59:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 21:04:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 22:04:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-01 23:09:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 00:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 01:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 01:54:50" level=info msg="GET / - 200 - 511.666µs"
time="2025-08-02 02:01:59" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 40.369499389s"
time="2025-08-02 02:02:03" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.403790597s"
time="2025-08-02 02:02:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-02 02:02:22" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 16.581554368s"
time="2025-08-02 02:03:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-02 02:03:33" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 34.792229064s"
time="2025-08-02 02:06:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:06:22" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 30.899208157s"
time="2025-08-02 02:08:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:08:14" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 31.055287159s"
time="2025-08-02 02:13:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:13:25" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.050110743s"
time="2025-08-02 02:14:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:14:19" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 39.14044885s"
time="2025-08-02 02:15:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:15:28" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 35.091034367s"
time="2025-08-02 02:19:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 02:26:42" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 33.847206686s"
time="2025-08-02 02:27:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:28:05" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 36.894085903s"
time="2025-08-02 02:28:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:30:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:30:24" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 28.43290609s"
time="2025-08-02 02:56:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 02:56:52" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 58.154975342s"
time="2025-08-02 03:01:54" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 42.237511003s"
time="2025-08-02 03:02:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 03:03:32" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 26.869042262s"
time="2025-08-02 03:04:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 03:04:46" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 39.139864645s"
time="2025-08-02 03:05:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 03:06:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 03:06:33" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 33.424388135s"
time="2025-08-02 03:24:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 03:50:58" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 35.582512768s"
time="2025-08-02 03:51:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 03:58:50" level=info msg="GET / - 200 - 1.036783ms"
time="2025-08-02 04:29:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 05:34:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 06:39:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 07:11:43" level=info msg="GET / - 200 - 639.891µs"
time="2025-08-02 07:44:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 08:49:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 09:49:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 10:54:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 11:59:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 12:59:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 13:59:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 15:04:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 15:13:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 15:13:21" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 51.940149644s"
time="2025-08-02 15:13:24" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.200245235s"
time="2025-08-02 15:14:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 15:24:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 15:24:29" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 35.711162287s"
time="2025-08-02 15:33:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-02 15:33:16" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 36.334186994s"
time="2025-08-02 16:04:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 17:09:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 18:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 18:56:47" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 29.34880988s"
time="2025-08-02 18:56:49" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.246735173s"
time="2025-08-02 18:57:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-02 19:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 20:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 21:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 22:19:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-02 23:24:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 00:24:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 00:57:16" level=info msg="GET / - 200 - 530.12µs"
time="2025-08-03 01:29:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 02:29:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 03:34:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 04:39:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 05:44:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 06:49:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 07:49:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 08:54:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 09:59:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 10:20:47" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 30.2847792s"
time="2025-08-03 10:20:51" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 3.36901082s"
time="2025-08-03 10:21:08" level=info msg="Successfully flushed 2 request logs."
time="2025-08-03 10:27:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 10:27:39" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 42.692238661s"
time="2025-08-03 10:43:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 10:43:20" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 40.354846397s"
time="2025-08-03 10:49:55" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 38.075323391s"
time="2025-08-03 10:50:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 10:51:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 10:51:27" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 41.144116969s"
time="2025-08-03 10:52:42" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 22.757048556s"
time="2025-08-03 10:53:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 11:00:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 11:00:23" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 31.530559088s"
time="2025-08-03 11:00:26" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.143224586s"
time="2025-08-03 11:01:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 11:04:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 11:08:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 11:08:16" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 31.276838975s"
time="2025-08-03 12:09:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 13:09:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 14:14:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 15:03:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 15:03:16" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 57.091949294s"
time="2025-08-03 15:03:19" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.552285245s"
time="2025-08-03 15:04:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 15:19:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 16:19:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 17:24:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 18:00:08" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 18:00:22" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 23.670039691s"
time="2025-08-03 18:29:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 19:29:08" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 20:26:13" level=info msg="Shutting down server..."
time="2025-08-03 20:26:13" level=info msg="HTTP server has been shut down."
time="2025-08-03 20:26:13" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-03 20:26:13" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-03 20:26:13" level=info msg="CronChecker stopped gracefully."
time="2025-08-03 20:26:13" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-03 20:26:13" level=info msg="RequestLogService stopped gracefully."
time="2025-08-03 20:26:13" level=info msg="All background services stopped."
time="2025-08-03 20:26:13" level=info msg="Server exited gracefully"
time="2025-08-03 20:27:12" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-08-03 20:27:13" level=info msg="Starting as Master Node."
time="2025-08-03 20:27:13" level=info msg="Database auto-migration completed."
time="2025-08-03 20:27:13" level=info msg="System settings initialized in DB."
time="2025-08-03 20:27:13" level=info
time="2025-08-03 20:27:13" level=info msg="========= System Settings ========="
time="2025-08-03 20:27:13" level=info msg="  --- Basic Settings ---"
time="2025-08-03 20:27:13" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-08-03 20:27:13" level=info msg="    Request Log Retention: 7 days"
time="2025-08-03 20:27:13" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-03 20:27:13" level=info msg="  --- Request Behavior ---"
time="2025-08-03 20:27:13" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-03 20:27:13" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-03 20:27:13" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-03 20:27:13" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-03 20:27:13" level=info msg="    Max Idle Connections: 100"
time="2025-08-03 20:27:13" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-03 20:27:13" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-03 20:27:13" level=info msg="    Max Retries: 3"
time="2025-08-03 20:27:13" level=info msg="    Blacklist Threshold: 3"
time="2025-08-03 20:27:13" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-03 20:27:13" level=info msg="===================================="
time="2025-08-03 20:27:13" level=info
time="2025-08-03 20:27:13" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-03 20:27:13" level=info msg="Updating active key lists for all groups..."
time="2025-08-03 20:27:13" level=info
time="2025-08-03 20:27:13" level=info msg="======= Server Configuration ======="
time="2025-08-03 20:27:13" level=info msg="  --- Server ---"
time="2025-08-03 20:27:13" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-08-03 20:27:13" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-03 20:27:13" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-03 20:27:13" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-03 20:27:13" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-03 20:27:13" level=info msg="  --- Performance ---"
time="2025-08-03 20:27:13" level=info msg="    Max Concurrent Requests: 200"
time="2025-08-03 20:27:13" level=info msg="  --- Security ---"
time="2025-08-03 20:27:13" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-03 20:27:13" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-08-03 20:27:13" level=info msg="  --- Logging ---"
time="2025-08-03 20:27:13" level=info msg="    Log Level: info"
time="2025-08-03 20:27:13" level=info msg="    Log Format: text"
time="2025-08-03 20:27:13" level=info msg="    File Logging: true"
time="2025-08-03 20:27:13" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-03 20:27:13" level=info msg="  --- Dependencies ---"
time="2025-08-03 20:27:13" level=info msg="    Database: configured"
time="2025-08-03 20:27:13" level=info msg="    Redis: not configured"
time="2025-08-03 20:27:13" level=info msg="===================================="
time="2025-08-03 20:27:13" level=info
time="2025-08-03 20:27:13" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-03 20:27:13" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-08-03 20:27:13" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-08-03 20:27:13" level=info
time="2025-08-03 20:32:13" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 21:29:45" level=info msg="Shutting down server..."
time="2025-08-03 21:29:45" level=info msg="HTTP server has been shut down."
time="2025-08-03 21:29:45" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-03 21:29:45" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-03 21:29:45" level=info msg="CronChecker stopped gracefully."
time="2025-08-03 21:29:45" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-03 21:29:45" level=info msg="RequestLogService stopped gracefully."
time="2025-08-03 21:29:45" level=info msg="All background services stopped."
time="2025-08-03 21:29:45" level=info msg="Server exited gracefully"
time="2025-08-03 21:29:47" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-08-03 21:29:47" level=info msg="Starting as Master Node."
time="2025-08-03 21:29:47" level=info msg="Database auto-migration completed."
time="2025-08-03 21:29:47" level=info msg="System settings initialized in DB."
time="2025-08-03 21:29:47" level=info
time="2025-08-03 21:29:47" level=info msg="========= System Settings ========="
time="2025-08-03 21:29:47" level=info msg="  --- Basic Settings ---"
time="2025-08-03 21:29:47" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-08-03 21:29:47" level=info msg="    Request Log Retention: 7 days"
time="2025-08-03 21:29:47" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-03 21:29:47" level=info msg="  --- Request Behavior ---"
time="2025-08-03 21:29:47" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-03 21:29:47" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-03 21:29:47" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-03 21:29:47" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-03 21:29:47" level=info msg="    Max Idle Connections: 100"
time="2025-08-03 21:29:47" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-03 21:29:47" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-03 21:29:47" level=info msg="    Max Retries: 3"
time="2025-08-03 21:29:47" level=info msg="    Blacklist Threshold: 3"
time="2025-08-03 21:29:47" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-03 21:29:47" level=info msg="===================================="
time="2025-08-03 21:29:47" level=info
time="2025-08-03 21:29:47" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-03 21:29:47" level=info msg="Updating active key lists for all groups..."
time="2025-08-03 21:29:47" level=info
time="2025-08-03 21:29:47" level=info msg="======= Server Configuration ======="
time="2025-08-03 21:29:47" level=info msg="  --- Server ---"
time="2025-08-03 21:29:47" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-08-03 21:29:47" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-03 21:29:47" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-03 21:29:47" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-03 21:29:47" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-03 21:29:47" level=info msg="  --- Performance ---"
time="2025-08-03 21:29:47" level=info msg="    Max Concurrent Requests: 200"
time="2025-08-03 21:29:47" level=info msg="  --- Security ---"
time="2025-08-03 21:29:47" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-03 21:29:47" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-08-03 21:29:47" level=info msg="  --- Logging ---"
time="2025-08-03 21:29:47" level=info msg="    Log Level: info"
time="2025-08-03 21:29:47" level=info msg="    Log Format: text"
time="2025-08-03 21:29:47" level=info msg="    File Logging: true"
time="2025-08-03 21:29:47" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-03 21:29:47" level=info msg="  --- Dependencies ---"
time="2025-08-03 21:29:47" level=info msg="    Database: configured"
time="2025-08-03 21:29:47" level=info msg="    Redis: not configured"
time="2025-08-03 21:29:47" level=info msg="===================================="
time="2025-08-03 21:29:47" level=info
time="2025-08-03 21:29:47" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-03 21:29:47" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-08-03 21:29:47" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-08-03 21:29:47" level=info
time="2025-08-03 21:29:51" level=info msg="GET / - 200 - 22.279407ms"
time="2025-08-03 21:33:35" level=info msg="GET / - 200 - 86.274µs"
time="2025-08-03 21:33:50" level=info msg="Shutting down server..."
time="2025-08-03 21:33:50" level=info msg="HTTP server has been shut down."
time="2025-08-03 21:33:50" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-03 21:33:50" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-03 21:33:50" level=info msg="CronChecker stopped gracefully."
time="2025-08-03 21:33:50" level=info msg="RequestLogService stopped gracefully."
time="2025-08-03 21:33:50" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-03 21:33:50" level=info msg="All background services stopped."
time="2025-08-03 21:33:50" level=info msg="Server exited gracefully"
time="2025-08-03 21:33:52" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-08-03 21:33:52" level=info msg="Starting as Master Node."
time="2025-08-03 21:33:52" level=info msg="Database auto-migration completed."
time="2025-08-03 21:33:52" level=info msg="System settings initialized in DB."
time="2025-08-03 21:33:52" level=info
time="2025-08-03 21:33:52" level=info msg="========= System Settings ========="
time="2025-08-03 21:33:52" level=info msg="  --- Basic Settings ---"
time="2025-08-03 21:33:52" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-08-03 21:33:52" level=info msg="    Request Log Retention: 7 days"
time="2025-08-03 21:33:52" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-03 21:33:52" level=info msg="  --- Request Behavior ---"
time="2025-08-03 21:33:52" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-03 21:33:52" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-03 21:33:52" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-03 21:33:52" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-03 21:33:52" level=info msg="    Max Idle Connections: 100"
time="2025-08-03 21:33:52" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-03 21:33:52" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-03 21:33:52" level=info msg="    Max Retries: 3"
time="2025-08-03 21:33:52" level=info msg="    Blacklist Threshold: 3"
time="2025-08-03 21:33:52" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-03 21:33:52" level=info msg="===================================="
time="2025-08-03 21:33:52" level=info
time="2025-08-03 21:33:52" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-03 21:33:52" level=info msg="Updating active key lists for all groups..."
time="2025-08-03 21:33:52" level=info
time="2025-08-03 21:33:52" level=info msg="======= Server Configuration ======="
time="2025-08-03 21:33:52" level=info msg="  --- Server ---"
time="2025-08-03 21:33:52" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-08-03 21:33:52" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-03 21:33:52" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-03 21:33:52" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-03 21:33:52" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-03 21:33:52" level=info msg="  --- Performance ---"
time="2025-08-03 21:33:52" level=info msg="    Max Concurrent Requests: 200"
time="2025-08-03 21:33:52" level=info msg="  --- Security ---"
time="2025-08-03 21:33:52" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-03 21:33:52" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-08-03 21:33:52" level=info msg="  --- Logging ---"
time="2025-08-03 21:33:52" level=info msg="    Log Level: info"
time="2025-08-03 21:33:52" level=info msg="    Log Format: text"
time="2025-08-03 21:33:52" level=info msg="    File Logging: true"
time="2025-08-03 21:33:52" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-03 21:33:52" level=info msg="  --- Dependencies ---"
time="2025-08-03 21:33:52" level=info msg="    Database: configured"
time="2025-08-03 21:33:52" level=info msg="    Redis: not configured"
time="2025-08-03 21:33:52" level=info msg="===================================="
time="2025-08-03 21:33:52" level=info
time="2025-08-03 21:33:52" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-03 21:33:52" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-08-03 21:33:52" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-08-03 21:33:52" level=info
time="2025-08-03 21:33:52" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 21:33:56" level=info msg="GET / - 200 - 2.876934ms"
time="2025-08-03 21:36:59" level=info msg="Shutting down server..."
time="2025-08-03 21:36:59" level=info msg="HTTP server has been shut down."
time="2025-08-03 21:36:59" level=info msg="cache syncer stopped." syncer=groups
time="2025-08-03 21:36:59" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-08-03 21:36:59" level=info msg="CronChecker stopped gracefully."
time="2025-08-03 21:36:59" level=info msg="LogCleanupService stopped gracefully."
time="2025-08-03 21:36:59" level=info msg="RequestLogService stopped gracefully."
time="2025-08-03 21:36:59" level=info msg="All background services stopped."
time="2025-08-03 21:36:59" level=info msg="Server exited gracefully"
time="2025-08-03 21:37:01" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-08-03 21:37:01" level=info msg="Starting as Master Node."
time="2025-08-03 21:37:01" level=info msg="Database auto-migration completed."
time="2025-08-03 21:37:01" level=info msg="System settings initialized in DB."
time="2025-08-03 21:37:01" level=info
time="2025-08-03 21:37:01" level=info msg="========= System Settings ========="
time="2025-08-03 21:37:01" level=info msg="  --- Basic Settings ---"
time="2025-08-03 21:37:01" level=info msg="    App URL: http://127.0.0.1:3001"
time="2025-08-03 21:37:01" level=info msg="    Request Log Retention: 7 days"
time="2025-08-03 21:37:01" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-08-03 21:37:01" level=info msg="  --- Request Behavior ---"
time="2025-08-03 21:37:01" level=info msg="    Request Timeout: 600 seconds"
time="2025-08-03 21:37:01" level=info msg="    Connect Timeout: 15 seconds"
time="2025-08-03 21:37:01" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-08-03 21:37:01" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-08-03 21:37:01" level=info msg="    Max Idle Connections: 100"
time="2025-08-03 21:37:01" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-08-03 21:37:01" level=info msg="  --- Key & Group Behavior ---"
time="2025-08-03 21:37:01" level=info msg="    Max Retries: 3"
time="2025-08-03 21:37:01" level=info msg="    Blacklist Threshold: 3"
time="2025-08-03 21:37:01" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-08-03 21:37:01" level=info msg="===================================="
time="2025-08-03 21:37:01" level=info
time="2025-08-03 21:37:01" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-08-03 21:37:01" level=info msg="Updating active key lists for all groups..."
time="2025-08-03 21:37:01" level=info
time="2025-08-03 21:37:01" level=info msg="======= Server Configuration ======="
time="2025-08-03 21:37:01" level=info msg="  --- Server ---"
time="2025-08-03 21:37:01" level=info msg="    Listen Address: 127.0.0.1:3001"
time="2025-08-03 21:37:01" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-08-03 21:37:01" level=info msg="    Read Timeout: 60 seconds"
time="2025-08-03 21:37:01" level=info msg="    Write Timeout: 600 seconds"
time="2025-08-03 21:37:01" level=info msg="    Idle Timeout: 120 seconds"
time="2025-08-03 21:37:01" level=info msg="  --- Performance ---"
time="2025-08-03 21:37:01" level=info msg="    Max Concurrent Requests: 200"
time="2025-08-03 21:37:01" level=info msg="  --- Security ---"
time="2025-08-03 21:37:01" level=info msg="    Authentication: enabled (key loaded)"
time="2025-08-03 21:37:01" level=info msg="    CORS: enabled (Origins: https://gpt-load.yuh.cool, http://gpt-load.yuh.cool, http://127.0.0.1:3001, http://localhost:3001)"
time="2025-08-03 21:37:01" level=info msg="  --- Logging ---"
time="2025-08-03 21:37:01" level=info msg="    Log Level: info"
time="2025-08-03 21:37:01" level=info msg="    Log Format: text"
time="2025-08-03 21:37:01" level=info msg="    File Logging: true"
time="2025-08-03 21:37:01" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-08-03 21:37:01" level=info msg="  --- Dependencies ---"
time="2025-08-03 21:37:01" level=info msg="    Database: configured"
time="2025-08-03 21:37:01" level=info msg="    Redis: not configured"
time="2025-08-03 21:37:01" level=info msg="===================================="
time="2025-08-03 21:37:01" level=info
time="2025-08-03 21:37:01" level=info msg="cache reloaded successfully" syncer=groups
time="2025-08-03 21:37:01" level=info msg="GPT-Load proxy server started successfully on Version: 1.0.0"
time="2025-08-03 21:37:01" level=info msg="Server address: http://127.0.0.1:3001"
time="2025-08-03 21:37:01" level=info
time="2025-08-03 21:37:04" level=info msg="GET / - 200 - 15.39141ms"
time="2025-08-03 21:39:49" level=info msg="GET / - 200 - 2.815746ms"
time="2025-08-03 21:39:50" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 669.474µs"
time="2025-08-03 21:39:50" level=info msg="GET /assets/index-C-we6bTS.js - 200 - 142.025314ms"
time="2025-08-03 21:39:52" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 307.917µs"
time="2025-08-03 21:39:52" level=info msg="GET /assets/Login-BKCd_56e.js - 200 - 477.528µs"
time="2025-08-03 21:39:52" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 32.685406ms"
time="2025-08-03 21:40:07" level=info msg="HEAD / - 200 - 72.942µs"
time="2025-08-03 21:40:32" level=info msg="GET / - 200 - 290.802µs"
time="2025-08-03 21:42:02" level=info msg="GET / - 200 - 262.779µs"
time="2025-08-03 21:42:10" level=info msg="GET / - 200 - 289.703µs"
time="2025-08-03 21:42:37" level=info msg="GET / - 200 - 261.881µs"
time="2025-08-03 21:42:54" level=info msg="GET / - 200 - 337.763µs"
time="2025-08-03 21:42:55" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 777.073µs"
time="2025-08-03 21:42:55" level=info msg="GET /assets/Dashboard-DPf9KtA_.js - 200 - 854.054µs"
time="2025-08-03 21:42:55" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 192.519µs"
time="2025-08-03 21:42:55" level=warning msg="GET /api/tasks/status - 401 - 19.132µs"
time="2025-08-03 21:42:55" level=warning msg="GET /api/dashboard/stats - 401 - 17.351µs"
time="2025-08-03 21:42:55" level=warning msg="GET /api/groups/list - 401 - 127.834µs"
time="2025-08-03 21:42:55" level=warning msg="GET /api/dashboard/chart - 401 - 142.811µs"
time="2025-08-03 21:42:55" level=info msg="GET /login - 200 - 271.63µs"
time="2025-08-03 21:42:58" level=info msg="POST /api/auth/login - 200 - 16.826749ms"
time="2025-08-03 21:42:58" level=info msg="GET /api/groups/list - 200 - 548.253µs"
time="2025-08-03 21:42:58" level=info msg="GET /api/tasks/status - 200 - 87.262µs"
time="2025-08-03 21:42:58" level=info msg="GET /api/dashboard/chart - 200 - 494.432µs"
time="2025-08-03 21:42:58" level=info msg="GET /api/dashboard/stats - 200 - 2.44893ms"
time="2025-08-03 21:51:32" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.184682568s"
time="2025-08-03 21:52:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-03 22:37:01" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-08-03 22:42:35" level=warning msg="Key has reached blacklist threshold, disabling." keyID=25 threshold=3
time="2025-08-03 22:42:40" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 5.385212436s"
time="2025-08-03 22:42:42" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 1.709228799s"
time="2025-08-03 22:43:01" level=info msg="Successfully flushed 2 request logs."
time="2025-08-03 23:42:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 59.461119ms."
time="2025-08-04 00:00:01" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 26.721072674s"
time="2025-08-04 00:00:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 00:00:04" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.750971019s"
time="2025-08-04 00:01:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 00:47:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 58.131413ms."
time="2025-08-04 01:52:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 53.879824ms."
time="2025-08-04 02:57:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 62.124575ms."
time="2025-08-04 04:02:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 57.19501ms."
time="2025-08-04 05:07:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 53.285912ms."
time="2025-08-04 06:12:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 68.315704ms."
time="2025-08-04 07:17:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 57.686132ms."
time="2025-08-04 08:22:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 63.276935ms."
time="2025-08-04 09:27:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 64.36955ms."
time="2025-08-04 09:36:47" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 48.744271509s"
time="2025-08-04 09:36:51" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.554103356s"
time="2025-08-04 09:37:01" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 09:37:57" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 42.454006277s"
time="2025-08-04 09:38:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 09:50:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 09:50:18" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 42.280973422s"
time="2025-08-04 09:53:38" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 36.634192254s"
time="2025-08-04 09:54:01" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 09:54:15" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 29.432983136s"
time="2025-08-04 09:55:30" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 5.31009649s"
time="2025-08-04 09:56:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:01:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:01:35" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 40.169617219s"
time="2025-08-04 10:03:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:03:18" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 38.307193794s"
time="2025-08-04 10:08:58" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 28.576763414s"
time="2025-08-04 10:09:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:16:38" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 38.679137142s"
time="2025-08-04 10:17:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:20:36" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 1.666035645s"
time="2025-08-04 10:21:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:22:06" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 3.972754204s"
time="2025-08-04 10:22:22" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 2.912906721s"
time="2025-08-04 10:22:36" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 2.268629502s"
time="2025-08-04 10:22:48" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 2.788751975s"
time="2025-08-04 10:23:01" level=info msg="Successfully flushed 4 request logs."
time="2025-08-04 10:23:04" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 4.662955087s"
time="2025-08-04 10:23:49" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 3.133024555s"
time="2025-08-04 10:24:01" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 10:24:08" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 3.042804617s"
time="2025-08-04 10:25:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:26:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:26:31" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.841167108s"
time="2025-08-04 10:29:45" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 41.662496665s"
time="2025-08-04 10:30:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:32:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 80.48594ms."
time="2025-08-04 10:49:13" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 12.376432952s"
time="2025-08-04 10:50:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:54:08" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:streamGenerateContent?alt=sse - 200 - 2.68015905s"
time="2025-08-04 10:55:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 10:56:40" level=info msg="GET / - 200 - 813.649µs"
time="2025-08-04 10:56:52" level=info msg="GET /favicon.ico - 200 - 1.118667ms"
time="2025-08-04 10:58:37" level=info msg="GET /favicon.ico - 200 - 334.052µs"
time="2025-08-04 10:58:53" level=info msg="GET /security.txt - 200 - 234.747µs"
time="2025-08-04 11:03:49" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 26.973653202s"
time="2025-08-04 11:03:51" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 1.829837321s"
time="2025-08-04 11:04:01" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 11:04:42" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 20.559792126s"
time="2025-08-04 11:05:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:05:41" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 22.771388241s"
time="2025-08-04 11:06:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:08:40" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 38.200934033s"
time="2025-08-04 11:09:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:13:34" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 23.395766113s"
time="2025-08-04 11:14:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:15:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:15:12" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 30.662648066s"
time="2025-08-04 11:16:41" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 31.447999427s"
time="2025-08-04 11:17:01" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 11:17:20" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 25.3452829s"
time="2025-08-04 11:26:36" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 37.057046026s"
time="2025-08-04 11:27:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:27:51" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 29.335016357s"
time="2025-08-04 11:28:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:30:52" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 28.672665349s"
time="2025-08-04 11:31:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 11:37:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 54.436038ms."
time="2025-08-04 11:44:45" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 44.008178555s"
time="2025-08-04 11:45:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 12:42:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 54.458297ms."
time="2025-08-04 13:47:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 61.165958ms."
time="2025-08-04 14:23:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 14:23:45" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 1m3.731283684s"
time="2025-08-04 14:23:48" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.036366505s"
time="2025-08-04 14:24:01" level=info msg="Successfully flushed 1 request logs."
time="2025-08-04 14:52:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 67.692166ms."
time="2025-08-04 15:35:56" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-pro:streamGenerateContent?alt=sse - 200 - 44.122338174s"
time="2025-08-04 15:35:59" level=info msg="POST /proxy/gemini/v1beta/models/gemini-2.5-flash:generateContent - 200 - 2.038200727s"
time="2025-08-04 15:36:01" level=info msg="Successfully flushed 2 request logs."
time="2025-08-04 15:57:01" level=info msg="CronChecker: Group 'gemini' validation finished. Total checked: 1, became valid: 0. Duration: 92.328773ms."
