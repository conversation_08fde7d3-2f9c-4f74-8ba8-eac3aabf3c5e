#!/bin/bash

# GPT-Load 自动备份脚本
# 由管理脚本自动生成

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"; }

# 配置
SCRIPT_DIR="/root/workspace/gpt-load/sh"
LOG_FILE="/root/workspace/gpt-load/auto-backup.log"

# 记录日志
exec >> "$LOG_FILE" 2>&1

log_info "开始自动备份..."

# 检查数据库文件
if [ ! -f "/root/workspace/gpt-load/data/gpt-load.db" ]; then
    log_error "数据库文件不存在，跳过备份"
    exit 1
fi

# 执行备份（自动保留最新3个备份）
if "$SCRIPT_DIR/database.sh" backup "auto_$(date +%Y%m%d_%H%M%S)"; then
    log_success "自动备份完成，已自动保留最新3个备份"
else
    log_error "自动备份失败"
    exit 1
fi
