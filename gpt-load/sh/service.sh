#!/bin/bash

# GPT-Load 服务管理脚本
# 功能：管理 GPT-Load 服务的启动、停止、重启、状态检查等
# 版本：v1.0 - GPT-Load Edition
# 作者：AI Assistant

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${CYAN}${BOLD}$1${NC}"; }

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
SERVICE_NAME="gpt-load"
SERVICE_FILE="/etc/systemd/system/gpt-load.service"
BINARY_PATH="$PROJECT_DIR/gpt-load"
PID_FILE="$PROJECT_DIR/gpt-load.pid"
LOG_FILE="$PROJECT_DIR/data/logs/app.log"

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 检查 Go 环境
check_go() {
    if ! command -v go &> /dev/null; then
        log_error "Go 环境未安装，请先安装 Go"
        exit 1
    fi
}

# 构建二进制文件
build_binary() {
    log_info "构建 GPT-Load 二进制文件..."
    cd "$PROJECT_DIR"
    
    if go build -o gpt-load main.go; then
        log_success "二进制文件构建成功"
        chmod +x gpt-load
    else
        log_error "二进制文件构建失败"
        exit 1
    fi
}

# 创建 systemd 服务文件
create_service_file() {
    log_info "创建 systemd 服务文件..."
    
    cat > "$SERVICE_FILE" << EOF
[Unit]
Description=GPT-Load AI Proxy Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
ExecStart=$BINARY_PATH
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=5
Restart=on-failure
RestartSec=10
StandardOutput=append:$LOG_FILE
StandardError=append:$LOG_FILE

# 环境变量
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
EnvironmentFile=-$PROJECT_DIR/.env

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    log_success "systemd 服务文件创建完成"
}

# 启动服务
start_service() {
    log_header "========================================"
    log_header "           启动 GPT-Load 服务"
    log_header "========================================"
    
    # 检查服务是否已在运行
    if is_service_running; then
        log_warning "GPT-Load 服务已在运行"
        show_service_status
        return 0
    fi
    
    # 检查并构建二进制文件
    if [ ! -f "$BINARY_PATH" ]; then
        log_info "二进制文件不存在，开始构建..."
        build_binary
    fi
    
    # 创建或更新服务文件
    if [ ! -f "$SERVICE_FILE" ]; then
        create_service_file
    fi
    
    # 启动服务
    log_info "启动 GPT-Load 服务..."
    if systemctl start "$SERVICE_NAME"; then
        systemctl enable "$SERVICE_NAME"
        log_success "GPT-Load 服务启动成功"
        
        # 等待服务完全启动
        sleep 3
        show_service_status
    else
        log_error "GPT-Load 服务启动失败"
        show_service_logs
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_header "========================================"
    log_header "           停止 GPT-Load 服务"
    log_header "========================================"
    
    if ! is_service_running; then
        log_warning "GPT-Load 服务未运行"
        return 0
    fi
    
    log_info "停止 GPT-Load 服务..."
    if systemctl stop "$SERVICE_NAME"; then
        log_success "GPT-Load 服务已停止"
    else
        log_error "GPT-Load 服务停止失败"
        
        # 强制终止进程
        log_info "尝试强制终止进程..."
        pkill -f gpt-load || true
        log_warning "已强制终止相关进程"
    fi
}

# 重启服务
restart_service() {
    log_header "========================================"
    log_header "           重启 GPT-Load 服务"
    log_header "========================================"
    
    stop_service
    sleep 2
    start_service
}

# 重新构建并重启
rebuild_service() {
    log_header "========================================"
    log_header "         重新构建 GPT-Load 服务"
    log_header "========================================"
    
    # 停止服务
    if is_service_running; then
        stop_service
        sleep 2
    fi
    
    # 重新构建
    build_binary
    
    # 启动服务
    start_service
}

# 检查服务是否运行
is_service_running() {
    systemctl is-active --quiet "$SERVICE_NAME" 2>/dev/null
}

# 显示服务状态
show_service_status() {
    log_header "========================================"
    log_header "         GPT-Load 服务状态"
    log_header "========================================"
    
    # systemd 服务状态
    echo -e "${CYAN}Systemd 服务状态:${NC}"
    systemctl status "$SERVICE_NAME" --no-pager -l || true
    echo
    
    # 端口监听状态
    echo -e "${CYAN}端口监听状态:${NC}"
    if netstat -tlnp 2>/dev/null | grep :3001; then
        log_success "✓ 端口 3001 正在监听"
    else
        log_error "✗ 端口 3001 未监听"
    fi
    echo
    
    # 进程状态
    echo -e "${CYAN}进程状态:${NC}"
    if pgrep -f gpt-load > /dev/null; then
        ps aux | grep gpt-load | grep -v grep
        log_success "✓ GPT-Load 进程正在运行"
    else
        log_error "✗ GPT-Load 进程未运行"
    fi
    echo
    
    # 服务连通性测试
    echo -e "${CYAN}服务连通性测试:${NC}"
    if curl -s http://127.0.0.1:3001/health > /dev/null 2>&1; then
        log_success "✓ 健康检查端点响应正常"
    else
        log_error "✗ 健康检查端点无响应"
    fi
    
    if curl -s http://127.0.0.1:3001 > /dev/null 2>&1; then
        log_success "✓ 主页访问正常"
    else
        log_error "✗ 主页访问失败"
    fi
}

# 显示服务日志
show_service_logs() {
    local lines="${1:-50}"
    local follow="${2:-false}"
    
    log_header "========================================"
    log_header "         GPT-Load 服务日志"
    log_header "========================================"
    
    if [ "$follow" = "true" ]; then
        log_info "实时跟踪服务日志 (Ctrl+C 退出)..."
        journalctl -u "$SERVICE_NAME" -f
    else
        log_info "显示最近 $lines 行日志..."
        journalctl -u "$SERVICE_NAME" -n "$lines" --no-pager
    fi
}

# 进入服务环境
enter_service() {
    log_info "进入 GPT-Load 项目目录..."
    cd "$PROJECT_DIR"
    exec bash
}

# 显示帮助信息
show_help() {
    echo "GPT-Load 服务管理脚本"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  start                   启动 GPT-Load 服务"
    echo "  stop                    停止 GPT-Load 服务"
    echo "  restart                 重启 GPT-Load 服务"
    echo "  rebuild                 重新构建并重启服务"
    echo "  status                  显示服务状态"
    echo "  logs [行数]             显示服务日志 (默认50行)"
    echo "  follow                  实时跟踪服务日志"
    echo "  enter                   进入服务环境"
    echo "  help                    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 start                # 启动服务"
    echo "  $0 logs 100             # 显示最近100行日志"
    echo "  $0 follow               # 实时跟踪日志"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_root
            check_go
            start_service
            ;;
        "stop")
            check_root
            stop_service
            ;;
        "restart")
            check_root
            check_go
            restart_service
            ;;
        "rebuild")
            check_root
            check_go
            rebuild_service
            ;;
        "status")
            show_service_status
            ;;
        "logs")
            show_service_logs "${2:-50}"
            ;;
        "follow")
            show_service_logs "50" "true"
            ;;
        "enter")
            enter_service
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
