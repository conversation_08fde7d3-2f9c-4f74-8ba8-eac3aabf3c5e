#!/bin/bash

# GPT-Load 数据库管理脚本
# 功能：管理 SQLite 数据库的备份、恢复、维护等操作
# 版本：v1.0 - GPT-Load Edition
# 作者：AI Assistant

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${CYAN}${BOLD}$1${NC}"; }

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
DB_FILE="$PROJECT_DIR/data/gpt-load.db"
BACKUP_DIR="$PROJECT_DIR/backup"
LOG_FILE="$PROJECT_DIR/data/logs/database.log"

# 确保目录存在
ensure_directories() {
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$(dirname "$LOG_FILE")"
}

# 记录日志
log_to_file() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# 检查数据库文件
check_database() {
    log_header "========================================"
    log_header "         数据库连接检查"
    log_header "========================================"

    if ! safe_database_check; then
        return 1
    fi

    log_info "检查数据库文件: $DB_FILE"
    log_info "检查模式: 只读检查，不会修改任何数据"

    # 检查文件大小
    local file_size=$(stat -f%z "$DB_FILE" 2>/dev/null || stat -c%s "$DB_FILE" 2>/dev/null)
    log_info "数据库文件大小: $(numfmt --to=iec $file_size)"

    # 检查数据库完整性
    log_info "检查数据库完整性..."
    if sqlite3 "$DB_FILE" "PRAGMA integrity_check;" | grep -q "ok"; then
        log_success "✓ 数据库完整性检查通过"
    else
        log_error "✗ 数据库完整性检查失败"
        return 1
    fi

    # 检查表结构
    log_info "检查数据库表结构..."
    local tables=$(sqlite3 "$DB_FILE" ".tables")
    if [ -n "$tables" ]; then
        log_success "✓ 数据库表结构正常"
        echo "表列表: $tables"
    else
        log_warning "⚠ 数据库中没有表"
    fi

    # 显示统计信息
    show_database_stats

    return 0
}

# 安全的只读查询函数
safe_readonly_query() {
    local query="$1"
    local default_value="${2:-0}"

    # 使用只读模式打开数据库，确保不会意外修改数据
    sqlite3 "file:$DB_FILE?mode=ro" "$query" 2>/dev/null || echo "$default_value"
}

# 显示数据库统计信息
show_database_stats() {
    log_info "数据库统计信息（只读模式）:"

    # 分组统计
    local groups_count=$(safe_readonly_query "SELECT COUNT(*) FROM groups;")
    echo "  分组数量: $groups_count"

    # API密钥统计
    local keys_count=$(safe_readonly_query "SELECT COUNT(*) FROM api_keys;")
    local active_keys=$(safe_readonly_query "SELECT COUNT(*) FROM api_keys WHERE status = 'active';")
    echo "  API密钥总数: $keys_count"
    echo "  活跃密钥数: $active_keys"

    # 请求日志统计
    local logs_count=$(safe_readonly_query "SELECT COUNT(*) FROM request_logs;")
    echo "  请求日志数: $logs_count"

    # 最近活动
    local last_request=$(safe_readonly_query "SELECT datetime(created_at, 'localtime') FROM request_logs ORDER BY created_at DESC LIMIT 1;" "无")
    echo "  最近请求时间: $last_request"
}

# 备份数据库
backup_database() {
    local backup_name="${1:-$(date +%Y%m%d_%H%M%S)}"
    local backup_file="$BACKUP_DIR/gpt-load_backup_${backup_name}.db"

    log_header "========================================"
    log_header "           数据库备份"
    log_header "========================================"

    ensure_directories

    # 使用安全检查函数
    if ! safe_database_check; then
        return 1
    fi

    # 检查备份目录空间
    local available_space=$(df "$BACKUP_DIR" | awk 'NR==2 {print $4}')
    local db_size=$(stat -f%z "$DB_FILE" 2>/dev/null || stat -c%s "$DB_FILE" 2>/dev/null)
    if [ "$available_space" -lt $((db_size / 1024 + 10240)) ]; then
        log_error "备份目录空间不足，需要至少 $(numfmt --to=iec $((db_size + 10485760))) 空间"
        return 1
    fi

    log_info "开始备份数据库..."
    log_info "源文件: $DB_FILE"
    log_info "备份文件: $backup_file"
    log_info "备份方式: SQLite .backup 命令（在线备份，不影响运行服务）"

    # 使用 SQLite 的 .backup 命令进行在线备份（不会锁定数据库）
    if sqlite3 "$DB_FILE" ".backup '$backup_file'"; then
        log_success "数据库备份完成"

        # 验证备份文件
        if sqlite3 "$backup_file" "PRAGMA integrity_check;" | grep -q "ok"; then
            log_success "✓ 备份文件完整性验证通过"

            # 显示备份信息
            local backup_size=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file" 2>/dev/null)
            log_info "备份文件大小: $(numfmt --to=iec $backup_size)"

            # 记录到日志
            log_to_file "BACKUP SUCCESS: $backup_file"

            # 自动清理旧备份（保留最新3个）
            cleanup_old_backups 3

        else
            log_error "✗ 备份文件完整性验证失败"
            rm -f "$backup_file"
            return 1
        fi
    else
        log_error "数据库备份失败"
        return 1
    fi
}

# 恢复数据库
restore_database() {
    local backup_file="$1"

    log_header "========================================"
    log_header "           数据库恢复"
    log_header "========================================"

    if [ -z "$backup_file" ]; then
        log_error "请指定备份文件路径"
        return 1
    fi

    if [ ! -f "$backup_file" ]; then
        log_error "备份文件不存在: $backup_file"
        return 1
    fi

    # 验证备份文件
    log_info "验证备份文件完整性..."
    if ! sqlite3 "$backup_file" "PRAGMA integrity_check;" | grep -q "ok"; then
        log_error "备份文件损坏，无法恢复"
        return 1
    fi

    # 备份当前数据库
    if [ -f "$DB_FILE" ]; then
        local current_backup="$BACKUP_DIR/gpt-load_before_restore_$(date +%Y%m%d_%H%M%S).db"
        log_info "备份当前数据库到: $current_backup"
        cp "$DB_FILE" "$current_backup"
    fi

    # 停止服务（如果正在运行）
    log_info "检查服务状态..."
    local service_was_running=false
    if systemctl is-active --quiet gpt-load 2>/dev/null; then
        service_was_running=true
        log_info "停止 GPT-Load 服务..."
        systemctl stop gpt-load
        sleep 2
    fi

    # 恢复数据库
    log_info "恢复数据库..."
    log_info "从: $backup_file"
    log_info "到: $DB_FILE"

    if cp "$backup_file" "$DB_FILE"; then
        log_success "数据库恢复完成"

        # 验证恢复的数据库
        if sqlite3 "$DB_FILE" "PRAGMA integrity_check;" | grep -q "ok"; then
            log_success "✓ 恢复的数据库完整性验证通过"
            log_to_file "RESTORE SUCCESS: from $backup_file"
        else
            log_error "✗ 恢复的数据库损坏"
            return 1
        fi

        # 重启服务（如果之前在运行）
        if [ "$service_was_running" = true ]; then
            log_info "重启 GPT-Load 服务..."
            systemctl start gpt-load
            sleep 3

            if systemctl is-active --quiet gpt-load; then
                log_success "✓ GPT-Load 服务重启成功"
            else
                log_error "✗ GPT-Load 服务重启失败"
            fi
        fi

    else
        log_error "数据库恢复失败"
        return 1
    fi
}

# 安全检查数据库状态
safe_database_check() {
    # 检查数据库文件是否存在
    if [ ! -f "$DB_FILE" ]; then
        log_error "数据库文件不存在: $DB_FILE"
        return 1
    fi

    # 检查数据库是否可访问
    if ! sqlite3 "$DB_FILE" "SELECT 1;" >/dev/null 2>&1; then
        log_error "数据库文件无法访问或已损坏"
        return 1
    fi

    # 检查是否有其他进程正在写入数据库
    if lsof "$DB_FILE" 2>/dev/null | grep -q "gpt-load"; then
        log_info "检测到 GPT-Load 服务正在使用数据库（这是正常的）"
    fi

    return 0
}

# 维护数据库
maintain_database() {
    log_header "========================================"
    log_header "           数据库维护"
    log_header "========================================"

    if ! safe_database_check; then
        return 1
    fi

    log_info "开始数据库维护..."
    log_warning "注意：维护操作会暂时影响数据库性能，但不会影响数据完整性"

    # 分析数据库
    log_info "分析数据库统计信息..."
    sqlite3 "$DB_FILE" "ANALYZE;"
    log_success "✓ 数据库分析完成"

    # 重建索引
    log_info "重建数据库索引..."
    sqlite3 "$DB_FILE" "REINDEX;"
    log_success "✓ 索引重建完成"

    # 清理碎片
    log_info "清理数据库碎片..."
    local size_before=$(stat -f%z "$DB_FILE" 2>/dev/null || stat -c%s "$DB_FILE" 2>/dev/null)
    sqlite3 "$DB_FILE" "VACUUM;"
    local size_after=$(stat -f%z "$DB_FILE" 2>/dev/null || stat -c%s "$DB_FILE" 2>/dev/null)

    local saved_space=$((size_before - size_after))
    log_success "✓ 碎片清理完成"
    log_info "清理前大小: $(numfmt --to=iec $size_before)"
    log_info "清理后大小: $(numfmt --to=iec $size_after)"
    if [ $saved_space -gt 0 ]; then
        log_info "节省空间: $(numfmt --to=iec $saved_space)"
    fi

    # 完整性检查
    log_info "执行完整性检查..."
    if sqlite3 "$DB_FILE" "PRAGMA integrity_check;" | grep -q "ok"; then
        log_success "✓ 数据库完整性检查通过"
    else
        log_error "✗ 数据库完整性检查失败"
        return 1
    fi

    log_to_file "MAINTENANCE COMPLETED"
    log_success "数据库维护完成"
}

# 列出备份文件
list_backups() {
    log_header "========================================"
    log_header "           备份文件列表"
    log_header "========================================"

    ensure_directories

    if [ ! -d "$BACKUP_DIR" ] || [ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]; then
        log_info "备份目录为空: $BACKUP_DIR"
        return 0
    fi

    log_info "备份目录: $BACKUP_DIR"
    echo

    # 按时间排序显示备份文件
    local count=0
    for backup in $(ls -t "$BACKUP_DIR"/*.db 2>/dev/null); do
        if [ -f "$backup" ]; then
            count=$((count + 1))
            local filename=$(basename "$backup")
            local size=$(stat -f%z "$backup" 2>/dev/null || stat -c%s "$backup" 2>/dev/null)
            local date=$(stat -f%Sm -t "%Y-%m-%d %H:%M:%S" "$backup" 2>/dev/null || stat -c%y "$backup" 2>/dev/null | cut -d. -f1)

            echo "[$count] $filename"
            echo "    大小: $(numfmt --to=iec $size)"
            echo "    时间: $date"
            echo "    路径: $backup"
            echo
        fi
    done

    if [ $count -eq 0 ]; then
        log_info "没有找到备份文件"
    else
        log_info "共找到 $count 个备份文件"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    local keep_count="${1:-3}"

    log_info "清理旧备份文件（保留最新 $keep_count 个）..."

    ensure_directories

    # 获取所有备份文件，按时间排序
    local backup_files=($(ls -t "$BACKUP_DIR"/*.db 2>/dev/null))
    local total_count=${#backup_files[@]}

    if [ $total_count -le $keep_count ]; then
        log_info "当前备份数量 ($total_count) 不超过保留数量 ($keep_count)，无需清理"
        return 0
    fi

    local delete_count=$((total_count - keep_count))
    log_info "将删除 $delete_count 个旧备份文件"

    # 删除多余的备份文件
    for ((i=keep_count; i<total_count; i++)); do
        local file="${backup_files[$i]}"
        if [ -f "$file" ]; then
            log_info "删除旧备份: $(basename "$file")"
            rm -f "$file"
        fi
    done

    log_success "旧备份清理完成"
    log_to_file "CLEANUP: deleted $delete_count old backups, kept $keep_count"
}

# 显示帮助信息
show_help() {
    echo "GPT-Load 数据库管理脚本"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  check                   检查数据库连接和完整性"
    echo "  backup [名称]           备份数据库 (可选自定义名称)"
    echo "  restore <备份文件>      从备份文件恢复数据库"
    echo "  maintain                维护数据库 (分析、重建索引、清理碎片)"
    echo "  list                    列出所有备份文件"
    echo "  cleanup [保留数量]      清理旧备份 (默认保留3个)"
    echo "  help                    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 backup               # 自动命名备份"
    echo "  $0 backup my_backup     # 自定义名称备份"
    echo "  $0 restore /path/to/backup.db"
    echo "  $0 cleanup 5            # 只保留最新5个备份"
}

# 主函数
main() {
    case "${1:-help}" in
        "check")
            check_database
            ;;
        "backup")
            backup_database "$2"
            ;;
        "restore")
            restore_database "$2"
            ;;
        "maintain")
            maintain_database
            ;;
        "list")
            list_backups
            ;;
        "cleanup")
            cleanup_old_backups "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
