#!/bin/bash

# GPT-Load 代码更新管理脚本
# 功能：管理 Git 仓库的拉取、推送、分支切换等操作
# 版本：v1.0 - GPT-Load Edition
# 作者：AI Assistant

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${CYAN}${BOLD}$1${NC}"; }

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
PUBLIC_REPO="https://github.com/tbphp/gpt-load.git"
PRIVATE_REPO="https://github.com/CoolKingW/gpt-load.git"

# 检查 Git 环境
check_git() {
    if ! command -v git &> /dev/null; then
        log_error "Git 未安装，请先安装 Git"
        exit 1
    fi

    if [ ! -d "$PROJECT_DIR/.git" ]; then
        log_error "当前目录不是 Git 仓库: $PROJECT_DIR"
        exit 1
    fi
}

# 检查 Git 状态
check_git_status() {
    log_header "========================================"
    log_header "           Git 仓库状态检查"
    log_header "========================================"

    cd "$PROJECT_DIR"

    # 当前分支和提交
    local current_branch=$(git branch --show-current)
    local current_commit=$(git rev-parse --short HEAD)
    local commit_message=$(git log -1 --pretty=format:"%s")

    echo -e "${CYAN}基本信息:${NC}"
    echo "  当前分支: $current_branch"
    echo "  当前提交: $current_commit"
    echo "  提交信息: $commit_message"
    echo

    # 远程仓库
    echo -e "${CYAN}远程仓库:${NC}"
    git remote -v | while read line; do
        echo "  $line"
    done
    echo

    # 工作区状态
    echo -e "${CYAN}工作区状态:${NC}"
    if [ -n "$(git status --porcelain)" ]; then
        git status --short | while read line; do
            echo "  $line"
        done
    else
        echo "  ✓ 工作区干净"
    fi
    echo

    # 分支同步状态
    echo -e "${CYAN}分支同步状态:${NC}"
    git fetch --all --quiet 2>/dev/null || true

    local ahead=$(git rev-list --count HEAD ^origin/$current_branch 2>/dev/null || echo "0")
    local behind=$(git rev-list --count origin/$current_branch ^HEAD 2>/dev/null || echo "0")

    if [ "$ahead" -gt 0 ]; then
        echo "  ↑ 本地领先远程 $ahead 个提交"
    fi

    if [ "$behind" -gt 0 ]; then
        echo "  ↓ 本地落后远程 $behind 个提交"
    fi

    if [ "$ahead" -eq 0 ] && [ "$behind" -eq 0 ]; then
        echo "  ✓ 与远程分支同步"
    fi
}

# 显示仓库状态总览
show_repo_status() {
    log_header "========================================"
    log_header "         仓库状态总览"
    log_header "========================================"

    cd "$PROJECT_DIR"

    # 检查远程仓库配置
    echo -e "${CYAN}远程仓库配置:${NC}"

    if git remote | grep -q "origin"; then
        local origin_url=$(git remote get-url origin)
        echo "  Origin (私有库): $origin_url"
    else
        echo "  Origin: 未配置"
    fi

    if git remote | grep -q "public"; then
        local public_url=$(git remote get-url public)
        echo "  Public (公有库): $public_url"
    else
        echo "  Public: 未配置"
    fi
    echo

    # 检查更新情况
    echo -e "${CYAN}更新检查:${NC}"
    git fetch --all --quiet 2>/dev/null || true

    # 检查公有库更新
    if git remote | grep -q "public"; then
        local public_updates=$(git log --oneline HEAD..public/main 2>/dev/null | wc -l || echo "0")
        if [ "$public_updates" -gt 0 ]; then
            echo "  📦 公有库有 $public_updates 个新提交可用"
        else
            echo "  ✓ 公有库无新更新"
        fi
    fi

    # 检查私有库更新
    if git remote | grep -q "origin"; then
        local origin_updates=$(git log --oneline HEAD..origin/$(git branch --show-current) 2>/dev/null | wc -l || echo "0")
        if [ "$origin_updates" -gt 0 ]; then
            echo "  📦 私有库有 $origin_updates 个新提交可用"
        else
            echo "  ✓ 私有库无新更新"
        fi
    fi

    check_git_status
}

# 从当前远程仓库拉取
pull_from_current() {
    local force="${1:-false}"

    log_header "========================================"
    log_header "         从当前远程仓库拉取"
    log_header "========================================"

    cd "$PROJECT_DIR"

    # 检查工作区状态
    if [ -n "$(git status --porcelain)" ] && [ "$force" != "true" ]; then
        log_warning "工作区有未提交的更改:"
        git status --short
        echo
        read -p "是否暂存这些更改并继续？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            git stash push -m "Auto stash before pull $(date '+%Y-%m-%d %H:%M:%S')"
            log_info "更改已暂存"
        else
            log_error "请先处理未提交的更改"
            return 1
        fi
    fi

    local current_branch=$(git branch --show-current)
    log_info "拉取分支: $current_branch"

    if git pull origin "$current_branch"; then
        log_success "代码拉取成功"

        # 检查是否有暂存的更改
        if git stash list | grep -q "Auto stash before pull"; then
            read -p "是否恢复之前暂存的更改？(y/N): " confirm
            if [[ $confirm == [yY] ]]; then
                git stash pop
                log_info "暂存的更改已恢复"
            fi
        fi
    else
        log_error "代码拉取失败"
        return 1
    fi
}

# 从公有库拉取
pull_from_public() {
    local force="${1:-false}"

    log_header "========================================"
    log_header "         从公有库拉取最新代码"
    log_header "========================================"

    cd "$PROJECT_DIR"

    # 检查是否已添加公有库远程仓库
    if ! git remote | grep -q "public"; then
        log_info "添加公有库远程仓库..."
        git remote add public "$PUBLIC_REPO"
        log_success "公有库远程仓库已添加"
    fi

    # 获取公有库最新代码
    log_info "获取公有库最新更新..."
    if git fetch public; then
        log_success "公有库代码获取成功"

        # 检查是否有新的更新
        local updates=$(git log --oneline HEAD..public/main 2>/dev/null | wc -l)
        if [ "$updates" -eq 0 ]; then
            log_info "公有库没有新的更新"
            return 0
        fi

        log_info "发现公有库 $updates 个新提交:"
        git log --oneline HEAD..public/main 2>/dev/null | head -5
        if [ "$updates" -gt 5 ]; then
            echo "  ... 还有 $((updates - 5)) 个提交"
        fi
        echo

        if [ "$force" = "true" ]; then
            confirm="y"
        else
            read -p "是否从公有库合并最新代码？(y/N): " confirm
        fi

        if [[ $confirm == [yY] ]]; then
            # 检查工作区状态
            if [ -n "$(git status --porcelain)" ]; then
                git stash push -m "Auto stash before public merge $(date '+%Y-%m-%d %H:%M:%S')"
                log_info "工作区更改已暂存"
            fi

            if git merge public/main; then
                log_success "公有库代码合并成功"

                # 恢复暂存的更改
                if git stash list | grep -q "Auto stash before public merge"; then
                    git stash pop 2>/dev/null || log_warning "暂存的更改可能存在冲突，请手动处理"
                fi
            else
                log_error "公有库代码合并失败，可能存在冲突"
                log_info "请手动解决冲突后提交"
                return 1
            fi
        fi
    else
        log_error "公有库代码获取失败"
        return 1
    fi
}

# 推送到私有库
push_to_private() {
    local force="${1:-false}"

    log_header "========================================"
    log_header "         推送代码到私有库"
    log_header "========================================"

    cd "$PROJECT_DIR"

    # 检查是否配置了私有库
    if ! git remote | grep -q "origin"; then
        log_info "配置私有库远程仓库..."
        git remote add origin "$PRIVATE_REPO"
        log_success "私有库远程仓库已配置"
    fi

    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "检测到未提交的更改:"
        git status --short
        echo

        read -p "是否提交这些更改？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            read -p "请输入提交信息 (回车使用默认): " commit_msg
            if [ -z "$commit_msg" ]; then
                commit_msg="Auto commit $(date '+%Y-%m-%d %H:%M:%S')"
            fi

            git add .
            git commit -m "$commit_msg"
            log_success "更改已提交: $commit_msg"
        else
            log_error "存在未提交的更改，无法推送"
            return 1
        fi
    fi

    # 推送到私有库
    local current_branch=$(git branch --show-current)
    log_info "推送分支 '$current_branch' 到私有库..."

    if [ "$force" = "true" ]; then
        if git push origin "$current_branch" --force; then
            log_success "强制推送到私有库成功"
            log_warning "注意：强制推送会覆盖远程历史记录"
        else
            log_error "强制推送失败"
            return 1
        fi
    else
        if git push origin "$current_branch"; then
            log_success "推送到私有库成功"
        else
            log_error "推送失败"
            read -p "是否强制推送？(y/N): " confirm
            if [[ $confirm == [yY] ]]; then
                push_to_private "true"
            else
                return 1
            fi
        fi
    fi

    local current_commit=$(git rev-parse --short HEAD)
    log_info "推送的提交: $current_commit"
}

# 完整更新流程
complete_update() {
    local force="${1:-false}"

    log_header "========================================"
    log_header "         完整更新流程"
    log_header "========================================"

    log_info "开始完整更新流程..."

    # 1. 检查状态
    log_info "步骤 1/4: 检查当前状态"
    check_git_status
    echo

    # 2. 从公有库拉取
    log_info "步骤 2/4: 从公有库拉取最新代码"
    if ! pull_from_public "$force"; then
        log_error "从公有库拉取失败，停止更新流程"
        return 1
    fi
    echo

    # 3. 构建前端（如果需要）
    log_info "步骤 3/4: 检查是否需要重新构建前端"
    if [ -d "$PROJECT_DIR/web" ] && [ -f "$PROJECT_DIR/web/package.json" ]; then
        read -p "是否重新构建前端？(y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            log_info "重新构建前端..."
            cd "$PROJECT_DIR/web"
            npm run build
            cd "$PROJECT_DIR"
            log_success "前端构建完成"
        fi
    fi
    echo

    # 4. 推送到私有库
    log_info "步骤 4/4: 推送到私有库"
    if [ "$force" = "true" ]; then
        confirm="y"
    else
        read -p "是否推送到私有库？(y/N): " confirm
    fi

    if [[ $confirm == [yY] ]]; then
        push_to_private "$force"
    else
        log_info "跳过推送到私有库"
    fi

    log_success "完整更新流程完成"
}

# 切换分支
switch_branch() {
    local branch_name="$1"

    if [ -z "$branch_name" ]; then
        log_error "请指定分支名称"
        return 1
    fi

    log_header "========================================"
    log_header "           切换分支"
    log_header "========================================"

    cd "$PROJECT_DIR"

    # 检查工作区状态
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "工作区有未提交的更改，将自动暂存"
        git stash push -m "Auto stash before branch switch $(date '+%Y-%m-%d %H:%M:%S')"
    fi

    # 切换分支
    if git checkout "$branch_name"; then
        log_success "已切换到分支: $branch_name"

        # 恢复暂存的更改
        if git stash list | grep -q "Auto stash before branch switch"; then
            read -p "是否恢复之前暂存的更改？(y/N): " confirm
            if [[ $confirm == [yY] ]]; then
                git stash pop
                log_info "暂存的更改已恢复"
            fi
        fi
    else
        log_error "分支切换失败"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "GPT-Load 代码更新管理脚本"
    echo
    echo "用法: $0 <命令> [参数]"
    echo
    echo "命令:"
    echo "  check                   检查 Git 状态"
    echo "  repo-status             显示仓库状态总览"
    echo "  pull [--force]          从当前远程仓库拉取"
    echo "  pull-public [--force]   从公有库拉取最新代码"
    echo "  push-private [--force]  推送代码到私有库"
    echo "  update [--force]        完整更新流程 (拉取+构建+推送)"
    echo "  switch <分支名>         切换分支"
    echo "  help                    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 pull-public          # 从公有库拉取"
    echo "  $0 push-private --force # 强制推送到私有库"
    echo "  $0 update               # 完整更新流程"
    echo "  $0 switch develop       # 切换到 develop 分支"
}

# 主函数
main() {
    check_git

    case "${1:-help}" in
        "check")
            check_git_status
            ;;
        "repo-status")
            show_repo_status
            ;;
        "pull")
            pull_from_current "$2"
            ;;
        "pull-public")
            pull_from_public "$2"
            ;;
        "push-private")
            push_to_private "$2"
            ;;
        "update")
            complete_update "$2"
            ;;
        "switch")
            switch_branch "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
