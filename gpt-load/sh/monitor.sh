#!/bin/bash

# GPT-Load 监控脚本
# 功能：监控服务状态、系统资源、日志分析等
# 版本：v1.0 - GPT-Load Edition
# 作者：AI Assistant

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_header() { echo -e "${CYAN}${BOLD}$1${NC}"; }

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
SERVICE_NAME="gpt-load"
LOG_FILE="$PROJECT_DIR/data/logs/app.log"
DB_FILE="$PROJECT_DIR/data/gpt-load.db"

# 检查服务状态
check_service_status() {
    local score=0
    local max_score=10
    
    echo -e "${CYAN}服务状态检查:${NC}"
    
    # 1. Systemd 服务状态 (2分)
    if systemctl is-active --quiet "$SERVICE_NAME" 2>/dev/null; then
        log_success "✓ Systemd 服务运行正常"
        score=$((score + 2))
    else
        log_error "✗ Systemd 服务未运行"
    fi
    
    # 2. 进程状态 (2分)
    if pgrep -f gpt-load > /dev/null; then
        log_success "✓ GPT-Load 进程运行正常"
        score=$((score + 2))
    else
        log_error "✗ GPT-Load 进程未运行"
    fi
    
    # 3. 端口监听 (2分)
    if netstat -tlnp 2>/dev/null | grep -q :3001; then
        log_success "✓ 端口 3001 正在监听"
        score=$((score + 2))
    else
        log_error "✗ 端口 3001 未监听"
    fi
    
    # 4. 健康检查端点 (2分)
    if curl -s --max-time 5 http://127.0.0.1:3001/health > /dev/null 2>&1; then
        log_success "✓ 健康检查端点响应正常"
        score=$((score + 2))
    else
        log_error "✗ 健康检查端点无响应"
    fi
    
    # 5. 主页访问 (2分)
    if curl -s --max-time 5 http://127.0.0.1:3001 > /dev/null 2>&1; then
        log_success "✓ 主页访问正常"
        score=$((score + 2))
    else
        log_error "✗ 主页访问失败"
    fi
    
    echo "服务状态评分: $score/$max_score"
    return $score
}

# 检查API功能
check_api_status() {
    local score=0
    local max_score=6
    
    echo -e "${CYAN}API功能检查:${NC}"
    
    # 1. API状态端点 (2分)
    local api_response=$(curl -s --max-time 5 http://127.0.0.1:3001/api/dashboard/stats 2>/dev/null || echo "")
    if echo "$api_response" | grep -q '"code":0'; then
        log_success "✓ API状态端点正常"
        score=$((score + 2))
    else
        log_error "✗ API状态端点异常"
    fi
    
    # 2. 认证功能 (2分)
    local auth_response=$(curl -s --max-time 5 -X POST -H "Content-Type: application/json" \
        -d '{"auth_key":"yu138187.."}' http://127.0.0.1:3001/api/auth/login 2>/dev/null || echo "")
    if echo "$auth_response" | grep -q '"success":true'; then
        log_success "✓ 认证功能正常"
        score=$((score + 2))
    else
        log_error "✗ 认证功能异常"
    fi
    
    # 3. 数据库连接 (2分)
    if [ -f "$DB_FILE" ] && sqlite3 "$DB_FILE" "SELECT 1;" > /dev/null 2>&1; then
        log_success "✓ 数据库连接正常"
        score=$((score + 2))
    else
        log_error "✗ 数据库连接失败"
    fi
    
    echo "API功能评分: $score/$max_score"
    return $score
}

# 检查系统资源
check_system_resources() {
    local score=0
    local max_score=8
    
    echo -e "${CYAN}系统资源检查:${NC}"
    
    # 1. 内存使用率 (2分)
    local mem_usage=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
    local mem_usage_int=${mem_usage%.*}
    if [ "$mem_usage_int" -lt 80 ]; then
        log_success "✓ 内存使用率正常 ($mem_usage%)"
        score=$((score + 2))
    elif [ "$mem_usage_int" -lt 90 ]; then
        log_warning "⚠ 内存使用率较高 ($mem_usage%)"
        score=$((score + 1))
    else
        log_error "✗ 内存使用率过高 ($mem_usage%)"
    fi
    
    # 2. 磁盘使用率 (2分)
    local disk_usage=$(df "$PROJECT_DIR" | awk 'NR==2{print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        log_success "✓ 磁盘使用率正常 ($disk_usage%)"
        score=$((score + 2))
    elif [ "$disk_usage" -lt 90 ]; then
        log_warning "⚠ 磁盘使用率较高 ($disk_usage%)"
        score=$((score + 1))
    else
        log_error "✗ 磁盘使用率过高 ($disk_usage%)"
    fi
    
    # 3. CPU负载 (2分)
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_ratio=$(echo "$load_avg $cpu_cores" | awk '{printf "%.1f", $1/$2*100}')
    local load_ratio_int=${load_ratio%.*}
    
    if [ "$load_ratio_int" -lt 70 ]; then
        log_success "✓ CPU负载正常 ($load_avg, $load_ratio%)"
        score=$((score + 2))
    elif [ "$load_ratio_int" -lt 90 ]; then
        log_warning "⚠ CPU负载较高 ($load_avg, $load_ratio%)"
        score=$((score + 1))
    else
        log_error "✗ CPU负载过高 ($load_avg, $load_ratio%)"
    fi
    
    # 4. 文件描述符 (2分)
    local fd_usage=$(lsof -p $(pgrep gpt-load | head -1) 2>/dev/null | wc -l || echo "0")
    if [ "$fd_usage" -lt 1000 ]; then
        log_success "✓ 文件描述符使用正常 ($fd_usage)"
        score=$((score + 2))
    elif [ "$fd_usage" -lt 5000 ]; then
        log_warning "⚠ 文件描述符使用较多 ($fd_usage)"
        score=$((score + 1))
    else
        log_error "✗ 文件描述符使用过多 ($fd_usage)"
    fi
    
    echo "系统资源评分: $score/$max_score"
    return $score
}

# 检查日志状态
check_log_status() {
    local score=0
    local max_score=4
    
    echo -e "${CYAN}日志状态检查:${NC}"
    
    # 1. 日志文件存在性 (1分)
    if [ -f "$LOG_FILE" ]; then
        log_success "✓ 日志文件存在"
        score=$((score + 1))
        
        # 2. 日志文件大小 (1分)
        local log_size=$(stat -f%z "$LOG_FILE" 2>/dev/null || stat -c%s "$LOG_FILE" 2>/dev/null)
        local log_size_mb=$((log_size / 1024 / 1024))
        if [ "$log_size_mb" -lt 100 ]; then
            log_success "✓ 日志文件大小正常 (${log_size_mb}MB)"
            score=$((score + 1))
        else
            log_warning "⚠ 日志文件较大 (${log_size_mb}MB)"
        fi
        
        # 3. 最近错误检查 (2分)
        local recent_errors=$(tail -1000 "$LOG_FILE" 2>/dev/null | grep -i error | wc -l || echo "0")
        if [ "$recent_errors" -eq 0 ]; then
            log_success "✓ 最近无错误日志"
            score=$((score + 2))
        elif [ "$recent_errors" -lt 10 ]; then
            log_warning "⚠ 发现少量错误日志 ($recent_errors 条)"
            score=$((score + 1))
        else
            log_error "✗ 发现大量错误日志 ($recent_errors 条)"
        fi
    else
        log_error "✗ 日志文件不存在"
    fi
    
    echo "日志状态评分: $score/$max_score"
    return $score
}

# 完整系统监控
complete_monitor() {
    log_header "========================================"
    log_header "         GPT-Load 完整系统监控"
    log_header "========================================"
    
    local total_score=0
    local max_total_score=28
    
    echo
    check_service_status
    local service_score=$?
    total_score=$((total_score + service_score))
    
    echo
    check_api_status
    local api_score=$?
    total_score=$((total_score + api_score))
    
    echo
    check_system_resources
    local resource_score=$?
    total_score=$((total_score + resource_score))
    
    echo
    check_log_status
    local log_score=$?
    total_score=$((total_score + log_score))
    
    echo
    log_header "========================================"
    log_header "           监控总结"
    log_header "========================================"
    
    local percentage=$((total_score * 100 / max_total_score))
    
    echo "总体评分: $total_score/$max_total_score ($percentage%)"
    
    if [ "$percentage" -ge 90 ]; then
        log_success "🎉 系统运行状态优秀"
    elif [ "$percentage" -ge 70 ]; then
        log_success "✅ 系统运行状态良好"
    elif [ "$percentage" -ge 50 ]; then
        log_warning "⚠️  系统运行状态一般，建议检查"
    else
        log_error "❌ 系统运行状态较差，需要立即处理"
    fi
    
    # 生成监控报告
    generate_monitor_report "$total_score" "$max_total_score" "$percentage"
}

# 快速状态检查
quick_status() {
    log_header "========================================"
    log_header "         GPT-Load 快速状态检查"
    log_header "========================================"
    
    # 服务状态
    echo -e "${CYAN}服务状态:${NC}"
    if systemctl is-active --quiet "$SERVICE_NAME" 2>/dev/null; then
        log_success "✓ 服务运行中"
    else
        log_error "✗ 服务未运行"
    fi
    
    # 端口状态
    if netstat -tlnp 2>/dev/null | grep -q :3001; then
        log_success "✓ 端口 3001 监听中"
    else
        log_error "✗ 端口 3001 未监听"
    fi
    
    # API状态
    if curl -s --max-time 3 http://127.0.0.1:3001/health > /dev/null 2>&1; then
        log_success "✓ API 响应正常"
    else
        log_error "✗ API 无响应"
    fi
    
    # 资源使用
    echo -e "${CYAN}资源使用:${NC}"
    local mem_usage=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
    local disk_usage=$(df "$PROJECT_DIR" | awk 'NR==2{print $5}')
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    echo "  内存使用: $mem_usage%"
    echo "  磁盘使用: $disk_usage"
    echo "  系统负载: $load_avg"
}

# 生成监控报告
generate_monitor_report() {
    local score="$1"
    local max_score="$2"
    local percentage="$3"
    local report_file="$PROJECT_DIR/data/logs/monitor_report_$(date +%Y%m%d_%H%M%S).txt"
    
    mkdir -p "$(dirname "$report_file")"
    
    cat > "$report_file" << EOF
GPT-Load 系统监控报告
生成时间: $(date '+%Y-%m-%d %H:%M:%S')
======================================

总体评分: $score/$max_score ($percentage%)

详细检查结果:
- 服务状态: $(systemctl is-active "$SERVICE_NAME" 2>/dev/null || echo "inactive")
- 端口监听: $(netstat -tlnp 2>/dev/null | grep :3001 > /dev/null && echo "正常" || echo "异常")
- API响应: $(curl -s --max-time 3 http://127.0.0.1:3001/health > /dev/null 2>&1 && echo "正常" || echo "异常")
- 内存使用: $(free | awk 'NR==2{printf "%.1f%%", $3*100/$2}')
- 磁盘使用: $(df "$PROJECT_DIR" | awk 'NR==2{print $5}')
- 系统负载: $(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

建议:
EOF

    if [ "$percentage" -lt 70 ]; then
        echo "- 建议检查服务日志: journalctl -u $SERVICE_NAME -n 50" >> "$report_file"
        echo "- 建议检查系统资源使用情况" >> "$report_file"
        echo "- 建议检查数据库连接状态" >> "$report_file"
    fi
    
    log_info "监控报告已生成: $report_file"
}

# 实时监控
real_time_monitor() {
    log_header "========================================"
    log_header "         GPT-Load 实时监控"
    log_header "========================================"
    
    log_info "开始实时监控 (Ctrl+C 退出)..."
    
    while true; do
        clear
        echo "$(date '+%Y-%m-%d %H:%M:%S') - GPT-Load 实时监控"
        echo "========================================"
        
        quick_status
        
        echo
        echo "按 Ctrl+C 退出监控"
        sleep 5
    done
}

# 显示帮助信息
show_help() {
    echo "GPT-Load 监控脚本"
    echo
    echo "用法: $0 <命令>"
    echo
    echo "命令:"
    echo "  monitor                 完整系统监控 (生成评分报告)"
    echo "  quick                   快速状态检查"
    echo "  service                 服务状态检查"
    echo "  api                     API功能检查"
    echo "  resources               系统资源检查"
    echo "  logs                    日志状态检查"
    echo "  realtime                实时监控 (持续刷新)"
    echo "  help                    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 monitor              # 完整监控检查"
    echo "  $0 quick                # 快速状态检查"
    echo "  $0 realtime             # 实时监控"
}

# 主函数
main() {
    case "${1:-help}" in
        "monitor")
            complete_monitor
            ;;
        "quick")
            quick_status
            ;;
        "service")
            check_service_status
            ;;
        "api")
            check_api_status
            ;;
        "resources")
            check_system_resources
            ;;
        "logs")
            check_log_status
            ;;
        "realtime")
            real_time_monitor
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
