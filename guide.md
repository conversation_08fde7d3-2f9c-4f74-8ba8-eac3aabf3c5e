# Workspace 综合项目 AI 指南

## 🎯 项目概述

这是一个多项目集成的工作空间，包含多个独立但相关的项目，专为AI助手快速理解而设计：

### 核心项目
- **New-API 项目**: AI API 网关和管理系统（主要功能）✨ **支持AI路径功能**
- **Love 项目**: 浪漫的情侣专用网站（独立项目）
- **GPT-Load 项目**: 高性能AI接口透明代理服务（新增项目）✨ **支持自定义子路径部署**
- **Shared 资源**: 共享的脚本、配置和工具

### 架构特点
- 🏗️ **多项目架构**: 独立项目，统一管理
- 🚀 **分别部署**: 每个项目可独立部署和管理
- 🔗 **资源共享**: 共享配置、脚本和工具
- 📦 **统一仓库**: 所有项目在一个Git仓库中管理
- ⚙️ **独立维护**: 每个项目有独立的文档和配置
- 🔧 **模块化部署**: 新增deploy-all架构，模块化管理部署流程

### 🆕 重构亮点 (Deploy-All 架构)

#### 问题解决
- **原问题**: 单一 `deploy.sh` 脚本过于臃肿 (2000+ 行)
- **解决方案**: 拆分为模块化架构，每个模块负责特定功能
- **优势**: 易维护、可复用、功能清晰、错误隔离

#### 设计原则
- **模块化设计**: 功能分离，每个模块负责特定功能
- **利用现有脚本**: 调用现有的项目管理脚本，避免重复实现
- **优雅降级**: 某个模块失败不影响其他模块
- **详细日志**: 记录每个步骤的执行情况

## 📁 项目结构（AI 快速参考）

```
workspace/                       # 🏠 工作空间根目录
├── 📄 README.md                 # 工作空间总体说明
├── 📄 guide.md                  # 本文件 - AI项目指南
├── 📄 deploy.sh                 # 🚀 综合项目部署脚本 (重构版)
├── 📂 deploy-all/               # 🔧 模块化部署架构 (新增)
│   ├── 📄 guide.md              # Deploy-All 架构说明
│   ├── 📂 common/               # 公共模块
│   │   ├── 📄 colors.sh         # 颜色定义和日志函数
│   │   └── 📄 utils.sh          # 通用工具函数
│   ├── 📂 modules/              # 功能模块
│   │   ├── 📄 environment.sh    # 环境检查模块
│   │   ├── 📄 dependencies.sh   # 依赖安装模块
│   │   ├── 📄 new-api.sh        # New-API部署模块
│   │   ├── 📄 love.sh           # Love项目部署模块
│   │   ├── 📄 network.sh        # 网络配置模块(Nginx/SSL)
│   │   └── 📄 verification.sh   # 部署验证模块
│   └── 📂 scripts/              # 独立管理脚本
│       ├── 📄 backup.sh         # 数据备份脚本
│       ├── 📄 cleanup.sh        # 环境清理脚本
│       └── 📄 restore.sh        # 数据恢复脚本
├── 📂 new-api/                  # 🤖 New-API 项目目录 ✨ **支持AI路径功能**
│   ├── 📄 cmd.sh                # New-API 一键部署脚本
│   ├── 📄 deploy-newapi.sh      # 一键部署脚本 ✨ 已更新支持AI路径
│   ├── 📄 new-api-manager.sh    # 统一管理工具
│   ├── 📄 guide-newapi.md       # New-API 项目完整指南 ✨ 新增
│   ├── 📄 README.md             # New-API 项目文档
│   ├── 📄 docker-compose.yml    # Docker服务配置
│   ├── 📄 .env.template         # 环境变量模板
│   ├── 📂 scripts/              # New-API 管理脚本
│   │   └── 📂 maintenance/      # 维护脚本 ✨ 新增
│   │       └── 📄 update-with-ai-path.sh # AI路径更新脚本 ✨
│   ├── 📂 nginx-newapi/         # Nginx配置 ✨ 已更新
│   │   ├── 📄 default.conf      # Docker容器配置 ✨ 支持AI路径
│   │   ├── 📄 system-nginx.conf # 系统HTTPS配置 ✨ 支持AI路径
│   │   └── 📄 system-nginx-http.conf # 系统HTTP配置 ✨ 支持AI路径
│   ├── 📂 web/                  # 前端项目 ✨ 已更新
│   │   ├── 📂 dist/             # 构建输出 ✨ 支持AI路径
│   │   ├── 📄 vite.config.js    # 构建配置 ✨ 相对路径支持
│   │   └── 📄 src/index.js      # 路由配置 ✨ AI路径basename
│   ├── 📂 backup/               # 数据库备份存储
│   ├── 📂 data/                 # Docker数据卷
│   └── 📂 [源代码]/             # Go后端 + React前端 ✨ 路由已更新
├── 📂 love/                     # 💕 Yu & Wang 情侣网站项目目录
│   ├── 📄 README.md             # 情侣网站项目文档
│   ├── 📄 package.json          # Node.js 项目配置
│   ├── 📄 server.js             # 后端服务器 (Express + SQLite)
│   ├── 📄 index.html            # 前端主页面 (Yu 💕 Wang)
│   ├── 📄 style.css             # 样式文件 (渐变字体设计)
│   ├── 📄 script.js             # 前端脚本 (计数器+留言+模态框)
│   ├── 📄 love_messages.db      # SQLite数据库 (留言存储)
│   ├── 📄 migrate-database.js   # 数据库迁移脚本
│   ├── 📄 deploy.sh             # Love项目部署脚本
│   ├── 📄 start-backend.sh      # 后端启动脚本
│   ├── 📄 setup-nginx.sh        # Nginx配置脚本
│   ├── 📄 test-api.html         # API测试页面
│   ├── 📄 test-font.html        # 字体测试页面
│   └── 📂 node_modules/         # Node.js 依赖
├── 📂 gpt-load/                 # 🤖 GPT-Load AI代理服务项目目录 ✨ **新增项目**
│   ├── 📄 manage-gptload.sh     # GPT-Load一键管理脚本
│   ├── 📄 gpt-load              # 编译后的Go二进制文件
│   ├── 📄 .env                  # 后端环境配置
│   ├── 📄 guide-gptload.md      # GPT-Load项目完整指南
│   ├── 📂 data/                 # 数据目录
│   │   ├── gpt-load.db          # SQLite数据库
│   │   └── logs/                # 应用日志
│   ├── 📂 backups/              # 备份目录
│   │   ├── gpt-load-backup-*.tar.gz # 数据库备份
│   │   └── *.custom             # 自定义配置备份
│   ├── 📂 web/                  # 前端源码目录
│   │   ├── dist/                # 构建输出(嵌入到Go程序)
│   │   ├── src/                 # Vue.js源码
│   │   ├── vite.config.ts       # 🔑 Vite构建配置(支持子路径)
│   │   └── .env.production      # 🔑 生产环境变量
│   └── 📂 internal/             # Go后端源码
└── 📂 shared/                   # 🔧 共享资源目录（备用配置和向后兼容）
    ├── 📄 README.md             # 共享资源说明（已更新弃用说明）
    ├── � STATUS.md             # Shared目录状态详细说明
    ├── �📂 scripts/              # 已弃用的管理脚本
    │   ├── workspace-manager.sh # 跨项目管理（已弃用，请使用各项目独立脚本）
    │   └── health-monitor.sh    # 健康监控（已弃用，功能已集成到Love管理脚本）
    └── 📂 nginx/                # 备用Nginx配置和SSL证书备份
        ├── system-nginx.conf    # 备用系统配置（当Love完整配置不可用时）
        ├── default.conf         # 过时配置（仅作参考）
        └── ssl/                 # Let's Encrypt证书备份
```

## 🔧 核心组件（AI 必知）

### 1. 🏗️ 多项目架构
- **workspace根目录**: 只有一个 `deploy.sh` 一键部署脚本
- **独立项目管理**: 每个项目通过独立的管理脚本进行管理
- **共享资源**: shared目录用于备用配置和向后兼容
- **统一仓库**: 所有项目在一个Git仓库中管理

### 2. 🌐 网络配置架构（重要）

#### 当前生产配置
- **域名**: `liangliangdamowang.edu.deal`
- **SSL证书**: Let's Encrypt自动续期
- **Nginx配置**: 使用Love项目的完整配置文件

#### 域名路径映射 ✨ **已更新支持AI路径**
```
https://liangliangdamowang.edu.deal/
├── /                    → New-API服务 (端口3000) ✅ 主要功能
├── /ai/                 → New-API AI路径前端 ✨ 新增功能
├── /api/*               → New-API原始API
├── /v1/*                → New-API原始OpenAI API
├── /ai/api/*            → New-API AI路径API ✨ 新增功能
├── /ai/v1/*             → New-API AI路径OpenAI API ✨ 新增功能
├── /ai/assets/*         → New-API AI路径静态资源 ✨ 新增功能
├── /new-api/            → New-API服务 (向后兼容路径)
├── /love/               → Love网站主页 ✅ 情侣网站
├── /love/api/           → Love API服务 (端口1314)
├── /love/style.css      → Love网站样式文件
├── /love/script.js      → Love网站脚本文件
├── /love/background/    → Love网站背景资源
├── /lgpt-load/          → GPT-Load AI代理服务 ✨ 新增功能
├── /lgpt-load/api/      → GPT-Load API服务 (端口3001)
├── /lgpt-load/assets/   → GPT-Load静态资源
└── /lgpt-load/auth/     → GPT-Load认证接口
```

#### 配置文件优先级
1. **生产使用**: `/root/workspace/love/config/nginx-complete.conf`
2. **备用配置**: `/root/workspace/shared/nginx/system-nginx.conf`
3. **历史参考**: `/root/workspace/shared/nginx/default.conf`（已过时）

### 2. 🤖 New-API 项目 ✨ **支持AI路径功能**
- **位置**: `./new-api/` 目录
- **部署**: `cd new-api && sudo ./deploy-newapi.sh` ✨ 推荐使用新脚本
- **管理**: `cd new-api && ./new-api-manager.sh`
- **功能**: AI API 网关和管理系统
- **文档**: 详见 `new-api/guide-newapi.md` ✨ 完整指南

#### 🌟 **AI路径功能特性**
- **双路径访问**: 原始路径 + AI路径 (`/ai/`)
- **API路径支持**: `/ai/api/*` 和 `/ai/v1/*`
- **向后兼容**: 原有路径继续可用
- **自动更新**: 专用更新脚本 `update-with-ai-path.sh`
- **完整备份**: 数据库和配置备份
- **SSL支持**: Let's Encrypt 自动证书
- **容器化部署**: Docker Compose 编排

#### 🔧 **技术架构更新**
- **前端配置**: 支持多路径部署 (`base: './'`, `basename="/ai"`)
- **后端路由**: 新增 `SetAIWebRouter` 函数处理AI路径
- **Nginx配置**: 全面更新支持AI路径的静态文件和API代理
- **构建系统**: 自动构建和部署AI路径前端

### 3. 💕 Love 项目 (Yu & Wang 情侣网站)
- **位置**: `./love/` 目录
- **技术栈**: Node.js + Express + SQLite (后端) + 现代化HTML5/CSS3/JS (前端)
- **域名配置**: 直接通过 `love` 域名访问，支持前后端分离架构
- **部署方式**: 一键式部署，自动配置Nginx反向代理

#### 🏠 核心功能特色
- **恋爱计数器**: 实时显示恋爱天数、小时、分钟
- **生日倒计时**: Yu (01月16日农历腊月初八) 和 Wang (04月15日) 的生日倒计时
- **三身份留言系统**: Yu / Wang / Other 身份留言，SQLite数据库存储
- **四个回忆卡片**: 第一次相遇、牵手、礼物、在一起的日子
- **浪漫星星互动**: 可点击的星星显示浪漫话语
- **现代化UI**: 渐变字体设计 (Dancing Script + Playfair Display)

#### 🏗️ 技术架构
- **前端**: HTML5 + CSS3 + JavaScript (现代化响应式设计)
- **后端**: Node.js + Express + SQLite3 (轻量级API服务)
- **数据库**: SQLite (love_messages.db) 存储留言数据
- **部署**: 一键式部署脚本，自动配置Nginx
- **域名**: 支持直接通过 `love` 域名访问

#### 🎨 技术特色
- **设计系统**: CSS变量 + 组件化样式 + 响应式设计
- **动画系统**: 心形飘落 + 流星效果 + 交互动画 + 滚动动画
- **字体系统**: Dancing Script (浪漫) + Playfair Display (优雅) + Inter (现代)
- **颜色系统**: Yu渐变 (紫蓝) + Wang渐变 (粉紫) + 统一色彩变量
- **组件系统**: 可复用的计数器、卡片、按钮、模态框组件

#### 🔧 开发和部署
- **访问地址**: 通过 `love` 域名直接访问
- **API端口**: 后端服务运行在 `localhost:3001`
- **部署脚本**: `./love/deploy.sh`
- **启动脚本**: `./love/start-backend.sh`
- **Nginx配置**: `./love/setup-nginx.sh` 自动配置反向代理
- **数据库**: SQLite (`love_messages.db`) 存储留言数据
- **一键部署**: 支持完整的一键式部署流程

#### 🚀 Love域名一键式部署指南
```bash
# 1. 进入love项目目录
cd love/

# 2. 一键部署后端服务
./deploy.sh
# 功能: 安装依赖 → 启动Node.js服务 → 创建数据库 → 配置Nginx → 后台运行

# 3. 一键配置域名和Nginx
./setup-nginx.sh
# 功能: 创建Nginx配置 → 配置反向代理 → 申请SSL证书 → 重启Nginx

# 4. 验证部署
curl http://localhost:3001/api/messages  # 测试API
# 通过love域名访问网站

# 5. 管理服务
./start-backend.sh                       # 启动/重启后端
ps aux | grep "node server.js"           # 检查服务状态
tail -f backend.log                      # 查看日志
```

#### 🌐 Nginx配置详情
- **配置文件**: 自动生成适合love域名的Nginx配置
- **反向代理**: 自动配置到Node.js后端 (端口3001)
- **SSL证书**: 支持Let's Encrypt自动申请
- **静态文件**: 直接服务前端HTML/CSS/JS文件
- **API路由**: `/api/` 路径代理到后端服务

### 4. 🤖 GPT-Load 项目 (AI接口透明代理服务) ✨ **新增项目**
- **位置**: `./gpt-load/` 目录
- **技术栈**: Go 1.24.3 + Gin Framework (后端) + Vue.js 3 + TypeScript + Vite (前端)
- **数据库**: SQLite (本地文件存储)
- **部署方式**: 本地源码编译 + 自定义子路径部署
- **访问地址**: https://liangliangdamowang.edu.deal/lgpt-load/
- **管理密码**: `123`

#### 🌟 **核心特性**
- **高性能代理**: AI接口透明代理，支持流式响应
- **自定义子路径**: 完美解决SPA应用子路径部署难题
- **本地编译部署**: 避免Docker镜像限制，支持完全自定义配置
- **智能配置管理**: 自动备份和恢复自定义配置
- **嵌入式前端**: Go程序编译时嵌入前端资源，单文件部署

#### 🏗️ 技术架构
- **前端**: Vue.js 3 + TypeScript + Vite + Naive UI
- **后端**: Go 1.24.3 + Gin Framework + 嵌入式前端资源
- **数据库**: SQLite (gpt-load.db) 本地文件存储
- **反向代理**: Nginx + 子路径重写 + 资源路径修复
- **服务管理**: systemd 服务 + 自动重启

#### 🔧 **自定义路径部署技术**
- **Vite配置**: `base: "/lgpt-load/"` 支持子路径构建
- **HTTP客户端**: `baseURL: import.meta.env.BASE_URL + "api"` 动态API路径
- **Vue Router**: `createWebHistory(import.meta.env.BASE_URL)` 自动路径适配
- **Nginx重写**: `rewrite ^/lgpt-load/(.*) /$1 break` 路径转发
- **资源修复**: `sub_filter` 修复HTML中的资源路径

#### 🔄 管理和维护
- **一键管理**: `./manage-gptload.sh` 完整管理脚本
- **智能更新**: 保护自定义配置的更新流程
- **数据备份**: 自动数据库备份，保留3个版本
- **服务管理**: `systemctl` 服务控制
- **配置保护**: 自动备份和恢复关键配置文件

#### 📱 功能特色
- **认证系统**: 简单密码认证 (默认: 123)
- **代理管理**: AI接口代理配置和管理
- **实时监控**: 服务状态和性能监控
- **日志管理**: 详细的应用日志记录
- **响应式UI**: 现代化Web界面，支持移动端

### 5. 🔧 Shared 共享资源
- **位置**: `./shared/` 目录
- **脚本**: 通用管理脚本
- **配置**: Nginx、SSL等共享配置
- **工具**: 跨项目的管理工具

#### 🆕 新Nginx架构 ✨ **重大升级**
- **架构类型**: 模块化配置管理系统
- **配置目录**: `./shared/nginx/`
- **核心特性**:
  - **conf.d/**: 项目配置文件（New-API、Love、GPT-Load）
  - **snippets/**: 可复用配置片段（SSL、代理、安全头、CORS、缓存）
  - **templates/**: 配置模板（project.tpl、spa.tpl）
  - **scripts/**: 管理脚本（项目添加、站点管理、SSL证书）
  - **backup/**: 完整配置备份和恢复系统
- **管理工具**: `./deploy-all/scripts/nginx-manager.sh` 扩展功能
- **优势**: 配置复用、自动化管理、快速扩展、版本控制

### 6. 🛠️ 工作空间级管理工具 (AI重点关注)
- **sync-configs.sh**: 配置同步工具
  - 同步shared配置到实际nginx配置
  - 验证所有服务状态
  - 测试外部访问
  - 显示配置摘要
- **verify-backups.sh**: 备份验证工具
  - 验证New-API和Love项目备份功能
  - 显示备份统计信息
  - 检查备份完整性
  - 提供备份建议
- **git-push-with-backup.sh**: Git备份推送工具
  - 自动备份所有数据库
  - 提交并推送代码到GitHub
  - 确保数据安全

#### New-API 项目脚本详情
- **位置**: `./new-api/scripts/` 目录
- **auto-backup-manager.sh**: 自动备份管理器
- **backup-database.sh**: 数据库备份脚本
- **manage-docker-services.sh**: Docker服务管理
- **quick-update.sh**: 快速更新脚本
- **restore-database.sh**: 数据库恢复脚本
- **update-new-api-core.sh**: 核心组件更新
- **update-project.sh**: 项目完整更新

### 7. 🎛️ 统一管理
- **项目级管理**: 每个项目有独立的管理工具
- **工作空间级管理**: 通过shared脚本统一管理
- **部署脚本**: `./deploy.sh` 综合部署脚本

## 🌐 域名配置

项目支持多域名结构：
- **主域名**: `liangliangdamowang.edu.deal` → New-API主服务 (端口3000)
- **情侣网站**: 通过 `love` 域名直接访问 → Yu & Wang 专属爱情网站
- **Love API**: `love` 域名的 `/api/` 路径 → 留言系统API (端口3001)
- **兼容路径**: `liangliangdamowang.edu.deal/new-api/` → New-API (向后兼容)

### Nginx 配置架构
- **Love域名配置**: 独立的Nginx配置文件支持 `love` 域名
- **自动配置**: `./love/setup-nginx.sh` 自动生成和配置Nginx
- **反向代理**: 自动配置到后端Node.js服务 (端口3001)
- **SSL证书**: 支持Let's Encrypt自动申请和续期
- **一键部署**: 完整的域名配置和SSL设置

### 当前路径映射 (AI必知)
```
love域名:
├── / → Love网站前端 (静态文件)
├── /api/ → Love后端API (127.0.0.1:3001)
└── /api/messages → 留言系统API

主域名 (liangliangdamowang.edu.deal):
├── / → New-API服务 (127.0.0.1:3000)
└── /new-api/ → New-API服务 (向后兼容)
```

## 💕 Love 项目详细功能 (Yu & Wang 情侣网站)

### 🎯 核心功能模块

#### 1. 💖 恋爱天数实时计算器
- **功能**: 实时显示从恋爱开始到现在的精确天数、小时数、分钟数
- **技术**: JavaScript setInterval 每分钟更新
- **样式**: 渐变色数字 + Playfair Display 字体
- **恋爱开始时间**: 可在 `script.js` 中的 `loveStartDate` 变量修改

#### 2. 🎂 生日倒计时系统
- **Yu的生日**: 01月16日 (农历腊月初八)
- **Wang的生日**: 04月15日
- **功能**: 自动计算距离下次生日的天数
- **样式**: 个性化渐变色 (Yu: 紫蓝色, Wang: 粉紫色)
- **字体**: Dancing Script 手写体 + 渐变色效果

#### 3. 💌 三身份留言系统
- **身份选择**: Yu / Wang / Other (访客)
- **功能**: 发布留言、查看所有留言、按身份筛选
- **数据存储**: SQLite 数据库 (`love_messages.db`)
- **API接口**:
  - `POST /api/messages` - 发布留言
  - `GET /api/messages` - 获取留言列表
- **样式**: 每个身份有独特的渐变色按钮设计

#### 4. 📖 回忆卡片详情系统
- **四个回忆主题**:
  - 第一次相遇 💫
  - 第一次牵手 🤝
  - 第一个物 🎁
  - 在一起的日子 💕
- **功能**: 点击卡片打开模态框，显示详细回忆内容
- **交互**: 平滑动画 + 响应式设计

#### 5. 🎨 视觉设计特色
- **字体组合**:
  - Dancing Script: 浪漫手写体 (名字、按钮)
  - Playfair Display: 优雅衬线体 (数字、标题)
  - Inter: 现代无衬线体 (正文)
- **配色方案**:
  - 紫蓝渐变: `#667eea` → `#764ba2` (Yu主色调)
  - 粉紫渐变: `#f093fb` → `#f5576c` (Wang主色调)
- **动画效果**: 浮动爱心、卡片悬停、按钮交互

### 🔧 技术架构

#### 前端技术栈
- **HTML5**: 语义化结构 + 响应式设计
- **CSS3**: Flexbox/Grid布局 + 渐变色 + 动画
- **JavaScript**: ES6+ 语法 + 模块化设计
- **字体**: Google Fonts (Dancing Script, Playfair Display, Inter)
- **图标**: Font Awesome 6.0

#### 后端技术栈
- **Node.js**: 运行环境
- **Express.js**: Web框架 + 路由管理
- **SQLite3**: 轻量级数据库
- **CORS**: 跨域资源共享
- **Body-parser**: 请求体解析

#### 数据库设计
```sql
-- messages 表结构
CREATE TABLE messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    author TEXT NOT NULL,           -- 'Yu', 'Wang', 'Other'
    content TEXT NOT NULL,          -- 留言内容
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP  -- 创建时间
);
```

### 📱 响应式设计
- **桌面端**: 完整功能展示
- **平板端**: 自适应布局调整
- **手机端**: 移动优化界面
- **断点**: 768px, 480px

## �🔄 工作流程（AI 操作指南）

### 全新环境部署（推荐）✨ **支持AI路径功能**
```bash
# 1. 克隆整个工作空间
git clone https://github.com/CoolKingW/workspace.git
cd workspace

# 2. 检查环境（可选但推荐）
./check-environment.sh

# 3. 一键部署所有项目（自动支持AI路径）
sudo ./deploy.sh

# 4. 或者分步部署
sudo ./deploy.sh --check      # 仅检查环境
sudo ./deploy.sh --new-api    # 仅部署New-API（包含AI路径功能）
sudo ./deploy.sh --love       # 仅部署Love项目
```

### 传统分项目部署 ✨ **已更新支持AI路径**
```bash
# 克隆整个工作空间
git clone https://github.com/CoolKingW/workspace.git
cd workspace

# 部署New-API项目（推荐使用新脚本）
cd new-api
sudo ./deploy-newapi.sh your-domain.com  # ✨ 自动支持AI路径功能
# 或使用传统脚本
sudo ./cmd.sh
cd ..

# 部署情侣网站项目
cd love
./deploy.sh
cd ..
```

### 项目管理

#### New-API 项目管理 ✨ **支持AI路径功能**
```bash
cd new-api
./new-api-manager.sh                    # 交互式管理界面
./new-api-manager.sh status             # 检查状态
./new-api-manager.sh restart            # 重启服务
./new-api-manager.sh backup             # 备份数据

# AI路径专用管理
./scripts/maintenance/update-with-ai-path.sh  # ✨ AI路径兼容更新
./deploy-newapi.sh your-domain.com            # ✨ 一键部署含AI路径功能
```

#### Love 项目管理 (Yu & Wang 情侣网站)
```bash
cd love
./start-backend.sh                      # 启动后端服务 (端口3001)
./deploy-love-backend.sh                # 重新部署后端服务
./setup-nginx.sh                        # 配置Nginx反向代理和域名
# 检查服务状态
ps aux | grep "node server.js"          # 检查后端进程
curl http://localhost:3001/api/messages # 测试API
# 或使用共享脚本
../shared/scripts/workspace-manager.sh status
../shared/scripts/workspace-manager.sh start
```

### 情侣网站开发 (Yu & Wang 专属功能)
```bash
# 1. 在 love/ 中编辑文件
cd love/
# 前端文件编辑:
# - index.html: 主页面结构 (恋爱计数器+生日倒计时+留言系统+回忆卡片)
# - style.css: 样式设计 (渐变字体+浪漫动画+响应式布局)
# - script.js: 前端逻辑 (实时计数器+留言CRUD+模态框+生日计算)
# 后端文件编辑:
# - server.js: Express服务器 (API路由+SQLite数据库+CORS配置)

# 2. 数据库操作
node migrate-database.js               # 运行数据库迁移
sqlite3 love_messages.db ".tables"     # 查看数据库表

# 3. 配置域名和Nginx (一键式)
./setup-nginx.sh                       # 自动配置love域名和SSL

# 4. 重启后端服务
./start-backend.sh

# 5. 测试功能
# 访问 http://localhost:3001 或通过love域名访问
# 测试API: curl http://localhost:3001/api/messages

# 6. 提交更改
cd ..
git add . && git commit -m "💕 更新Yu&Wang情侣网站功能"
```

### 跨项目管理
```bash
# 使用共享脚本管理多个项目
./shared/scripts/workspace-manager.sh status   # 检查所有服务状态
./new-api/scripts/backup-database.sh           # 备份数据库
./love/deploy-love-backend.sh                  # 部署情侣网站
```

## Environment Configuration

### Key Environment Variables (.env)
- `DOMAIN_NAME`: Main domain name
- `API_DOMAIN`: API subdomain
- `SSL_EMAIL`: Email for SSL certificates
- `MYSQL_ROOT_PASSWORD`: Database password
- `SESSION_SECRET`: Security secret

### Docker Services
- **new-api**: Main application container
- **mysql**: Database container
- **redis**: Cache container

## File Management

### Important Files to Track
- `love/`: Love website files (user edits here)
- `nginx/`: Nginx configurations
- `scripts/`: Management scripts
- `docker-compose.yml`: Service configuration
- `.env`: Environment variables (not in git)

### Files to Ignore
- `backup/*.sql`: Database backups
- `data/mysql/`: Database data files
- `logs/`: Application logs
- `.env`: Environment file with secrets

## 🤖 AI 助手操作指南

### 当用户想要：

1. **部署全新服务器（推荐）**
   - 克隆工作空间: `git clone https://github.com/CoolKingW/workspace.git`
   - 检查环境: `./check-environment.sh`
   - 一键部署: `sudo ./deploy.sh`
   - 分步部署: `sudo ./deploy.sh --new-api` 或 `sudo ./deploy.sh --love`
   - 说明部署后步骤（DNS配置、SSL证书）

2. **传统分项目部署**
   - New-API部署: `cd new-api && sudo ./cmd.sh`
   - 情侣网站部署: `cd love && ./deploy.sh`

2. **管理New-API项目**
   - 进入项目目录: `cd new-api`
   - **优先推荐**: `./new-api-manager.sh` 统一管理工具
   - 备选方案: `./scripts/` 目录中的独立脚本
     - `./scripts/backup-database.sh` - 数据库备份
     - `./scripts/restore-database.sh` - 数据库恢复
     - `./scripts/auto-backup-manager.sh` - 自动备份管理
     - `./scripts/manage-docker-services.sh` - Docker服务管理
     - `./scripts/quick-update.sh` - 快速更新
     - `./scripts/update-new-api-core.sh` - 核心组件更新
     - `./scripts/update-project.sh` - 项目完整更新
   - 查看日志: `docker-compose logs`

2.5. **工作空间级配置管理 (AI优先使用)**
   - **配置同步**: `./sync-configs.sh` - 确保所有配置文件一致
     - `./sync-configs.sh --nginx-only` - 仅同步Nginx配置
     - `./sync-configs.sh --verify-only` - 仅验证服务状态
     - `./sync-configs.sh --test-only` - 仅测试外部访问
   - **备份验证**: `./verify-backups.sh` - 检查备份功能完整性
     - `./verify-backups.sh --stats-only` - 仅显示备份统计
     - `./verify-backups.sh --new-api-only` - 仅验证New-API备份
   - **Git备份**: `./git-push-with-backup.sh` - 备份数据库并推送代码

3. **管理情侣网站项目**
   - 进入项目目录: `cd love`
   - 启动后端: `./start-backend.sh`
   - 部署更新: `./deploy.sh`
   - 配置域名: `./setup-nginx.sh`
   - 或使用共享脚本: `../shared/scripts/workspace-manager.sh`

4. **管理GPT-Load项目 (AI代理服务)** ✨ **新增项目**
   - 进入项目目录: `cd gpt-load`
   - **一键管理**: `./manage-gptload.sh` 完整管理脚本
     - `./manage-gptload.sh help` - 查看所有功能
     - `./manage-gptload.sh backup` - 数据库备份(自动保留3个)
     - `./manage-gptload.sh update` - 智能更新(保留自定义配置)
     - `./manage-gptload.sh service start|stop|restart|status|logs` - 服务管理
     - `./manage-gptload.sh build` - 重新构建项目
   - **服务管理**: `systemctl status gpt-load` - 检查服务状态
   - **访问地址**: https://liangliangdamowang.edu.deal/lgpt-load/
   - **管理密码**: `123`
   - **日志查看**: `journalctl -u gpt-load -f` - 实时日志
   - **配置文件**:
     - `web/vite.config.ts` - 前端构建配置
     - `web/.env.production` - 生产环境变量
     - `.env` - 后端配置

5. **修改情侣网站 (Yu & Wang 专属功能)**
   - 指导在 `love/` 中编辑文件
   - 前端文件:
     - `index.html`: 主页面结构 (恋爱计数器+生日倒计时+留言系统+回忆卡片)
     - `style.css`: 渐变字体设计 (Dancing Script + Playfair Display)
     - `script.js`: 前端逻辑 (实时计数器+留言CRUD+模态框)
   - 后端文件: `server.js` (Express + SQLite API)
   - 数据库: `love_messages.db` (SQLite留言存储)
   - 配置域名: `./setup-nginx.sh` (一键配置love域名)
   - 重启服务: `./start-backend.sh`
   - 测试功能: 通过love域名访问
   - 提交更改到 git

6. **Nginx配置管理** ✨ **新增功能**
   - **完整管理**: `./deploy-all/scripts/nginx-manager.sh --help` - 查看所有功能
   - **项目管理**:
     - `./deploy-all/scripts/nginx-manager.sh --add-project <domain> <name> <template>` - 添加新项目
     - `./deploy-all/scripts/nginx-manager.sh --list-templates` - 查看可用模板
     - `./deploy-all/scripts/nginx-manager.sh --enable-site <domain>` - 启用站点
     - `./deploy-all/scripts/nginx-manager.sh --disable-site <domain>` - 禁用站点
   - **SSL管理**: `./deploy-all/scripts/nginx-manager.sh --ssl-cert <domain>` - 申请SSL证书
   - **配置管理**:
     - `./deploy-all/scripts/nginx-manager.sh --backup` - 备份配置
     - `./deploy-all/scripts/nginx-manager.sh --status` - 查看状态
     - `./deploy-all/scripts/nginx-manager.sh --test` - 测试配置
   - **架构文档**: 查看 `./nginx.md` 获取完整使用指南

7. **跨项目管理**
   - 使用共享脚本: `./shared/scripts/workspace-manager.sh`
   - 备份数据: `./new-api/scripts/backup-database.sh`
   - 服务管理: `./shared/scripts/workspace-manager.sh status`
   - 情侣网站: `./love/deploy.sh`

8. **故障排除 (AI系统化诊断)**
   - **快速诊断**: `./sync-configs.sh --verify-only` - 一键检查所有服务状态
   - **配置问题**: `./sync-configs.sh` - 同步并修复配置不一致
   - **外部访问**: `./sync-configs.sh --test-only` - 测试域名访问
   - **备份问题**: `./verify-backups.sh` - 检查备份功能完整性

   **详细排查**:
   - New-API: `cd new-api && ./new-api-manager.sh status`
   - 情侣网站:
     - 检查进程: `cd love && ps aux | grep "node server.js"`
     - 检查端口: `netstat -tlnp | grep 3001`
     - 测试API: `curl http://localhost:3001/api/messages`
     - 查看日志: `cd love && tail -f backend.log`
     - 检查数据库: `sqlite3 love_messages.db ".tables"`
     - 检查域名配置: `nginx -t && systemctl status nginx`
   - GPT-Load项目: ✨ **新增诊断**
     - 检查服务: `cd gpt-load && systemctl status gpt-load`
     - 检查进程: `ps aux | grep gpt-load`
     - 检查端口: `netstat -tlnp | grep 3001`
     - 测试访问: `curl -I https://liangliangdamowang.edu.deal/lgpt-load/`
     - 查看日志: `journalctl -u gpt-load -f`
     - 管理脚本: `cd gpt-load && ./manage-gptload.sh service status`
     - 检查数据库: `sqlite3 data/gpt-load.db ".tables"`
     - 重新构建: `cd gpt-load && ./manage-gptload.sh build`
   - Nginx新架构: ✨ **新增诊断**
     - 架构状态: `./deploy-all/scripts/nginx-manager.sh --status`
     - 配置测试: `./deploy-all/scripts/nginx-manager.sh --test`
     - 配置冲突: `./deploy-all/scripts/nginx-manager.sh --conflicts`
     - 站点列表: `./deploy-all/scripts/nginx-manager.sh --list-templates`
     - 配置备份: `./deploy-all/scripts/nginx-manager.sh --backup`
   - Nginx配置: `nginx -t && systemctl status nginx`
   - SSL证书: `certbot certificates`

### 🔧 常用命令参考（AI 快速查询）
```bash
# 工作空间管理
git clone https://github.com/CoolKingW/workspace.git  # 克隆工作空间
./check-environment.sh                  # 检查系统环境
sudo ./deploy.sh                        # 一键部署所有项目
sudo ./deploy.sh --check                # 仅检查环境
sudo ./deploy.sh --new-api              # 仅部署New-API
sudo ./deploy.sh --love                 # 仅部署Love项目
./deploy.sh --help                      # 查看部署帮助

# New-API 项目管理 ✨ 支持AI路径功能
cd new-api                              # 进入New-API项目
sudo ./deploy-newapi.sh your-domain.com # ✨ 推荐：一键部署含AI路径功能
sudo ./cmd.sh                           # 传统一键部署
./new-api-manager.sh                    # 交互式管理界面
./new-api-manager.sh status             # 检查服务状态
./new-api-manager.sh restart            # 重启服务
./new-api-manager.sh backup             # 备份数据库
./scripts/maintenance/update-with-ai-path.sh # ✨ AI路径兼容更新
./test-deployment.sh                    # 测试部署环境

# 情侣网站项目管理
cd love                                 # 进入情侣网站项目
./start-backend.sh                      # 启动后端服务
./deploy.sh                             # 部署Love项目
./setup-nginx.sh                        # 配置Nginx和love域名

# GPT-Load 项目管理 ✨ 新增项目
cd gpt-load                             # 进入GPT-Load项目
./manage-gptload.sh help                # 查看所有管理功能
./manage-gptload.sh backup              # 数据库备份(自动保留3个)
./manage-gptload.sh update              # 智能更新(保留自定义配置)
./manage-gptload.sh service start       # 启动服务
./manage-gptload.sh service stop        # 停止服务
./manage-gptload.sh service restart     # 重启服务
./manage-gptload.sh service status      # 检查服务状态
./manage-gptload.sh service logs        # 查看服务日志
./manage-gptload.sh build               # 重新构建项目
systemctl status gpt-load               # 检查systemd服务状态
journalctl -u gpt-load -f               # 实时查看服务日志

# Nginx配置管理 ✨ 新增功能
./deploy-all/scripts/nginx-manager.sh --help        # 查看所有nginx管理功能
./deploy-all/scripts/nginx-manager.sh --status      # 查看nginx和项目状态
./deploy-all/scripts/nginx-manager.sh --backup      # 备份当前nginx配置
./deploy-all/scripts/nginx-manager.sh --test        # 测试nginx配置语法
./deploy-all/scripts/nginx-manager.sh --list-templates  # 查看可用配置模板
./deploy-all/scripts/nginx-manager.sh --add-project example.com myapp project  # 添加新项目
./deploy-all/scripts/nginx-manager.sh --enable-site example.com   # 启用站点
./deploy-all/scripts/nginx-manager.sh --disable-site example.com  # 禁用站点
./deploy-all/scripts/nginx-manager.sh --ssl-cert example.com      # 申请SSL证书

# 工作空间级管理工具 (AI优先使用)
./sync-configs.sh                       # 完整配置同步和验证
./sync-configs.sh --nginx-only          # 仅同步Nginx配置
./sync-configs.sh --verify-only         # 仅验证服务状态
./sync-configs.sh --test-only           # 仅测试外部访问
./verify-backups.sh                     # 完整备份验证
./verify-backups.sh --stats-only        # 仅显示备份统计
./git-push-with-backup.sh               # 备份数据库并推送Git

# 共享脚本管理 (备选方案)
./shared/scripts/workspace-manager.sh status    # 检查所有服务状态
./shared/scripts/workspace-manager.sh start     # 启动所有项目
./shared/scripts/workspace-manager.sh stop      # 停止所有项目
./shared/scripts/workspace-manager.sh restart   # 重启所有项目
./shared/scripts/workspace-manager.sh backup    # 备份所有项目数据

# New-API 项目管理
./new-api/scripts/backup-database.sh            # 备份数据库
./new-api/scripts/restore-database.sh           # 恢复数据库
./new-api/scripts/update-project.sh             # 更新项目
./new-api/scripts/auto-backup-manager.sh        # 自动备份管理
./new-api/scripts/manage-docker-services.sh     # Docker服务管理
./new-api/scripts/quick-update.sh               # 快速更新
./new-api/scripts/update-new-api-core.sh        # 更新核心组件

# Love 项目管理 (Yu & Wang 情侣网站)
./love/deploy.sh                                # 部署情侣网站
./love/start-backend.sh                         # 启动情侣网站后端 (端口3001)
./love/setup-nginx.sh                           # 配置Nginx反向代理和love域名
node ./love/migrate-database.js                 # 运行数据库迁移
sqlite3 ./love/love_messages.db ".tables"       # 查看数据库表
curl http://localhost:3001/api/messages         # 测试留言API

# SSL证书
sudo certbot --nginx -d domain.com      # 设置SSL
sudo certbot renew                      # 续期证书
```

## 🔒 安全考虑

- SSL证书通过 certbot 自动续期
- 数据库密码存储在环境变量中（自动生成随机密码）
- Nginx 安全头已配置
- 文件权限正确设置
- Git 忽略敏感文件

## 🎯 迁移优势

- **零配置**: 新服务器无需手动设置
- **完整备份**: 包含代码、数据和配置
- **快速恢复**: 几分钟内重建整个环境
- **版本控制**: 所有配置都在 git 中跟踪
- **统一管理**: 单一仓库管理所有内容

## 🤖 AI助手关键信息 (必读)

### 当前系统状态 (2025-07-25更新) ✨ **支持AI路径功能**
- **主域名**: `https://liangliangdamowang.edu.deal` → New-API服务正常运行 ✅
- **AI路径前端**: `https://liangliangdamowang.edu.deal/ai/` → New-API AI路径前端 ✨ 新增
- **AI路径API**: `https://liangliangdamowang.edu.deal/ai/api/*` → New-API AI路径API ✨ 新增
- **AI路径OpenAI**: `https://liangliangdamowang.edu.deal/ai/v1/*` → New-API AI路径OpenAI API ✨ 新增
- **Love网站**: `https://liangliangdamowang.edu.deal/love/` → 静态文件正常访问 ✅
- **Love API**: `https://liangliangdamowang.edu.deal/love/api/` → 后端API (端口1314) ✅
- **GPT-Load服务**: `https://liangliangdamowang.edu.deal/lgpt-load/` → AI代理服务正常运行 ✨ 新增
- **GPT-Load API**: `https://liangliangdamowang.edu.deal/lgpt-load/api/` → 代理API (端口3001) ✨ 新增
- **New-API兼容**: `https://liangliangdamowang.edu.deal/new-api/` → 向后兼容路径 ✅
- **SSL证书**: Let's Encrypt证书有效期至2025年10月15日
- **服务状态**: 所有服务运行正常，配置已优化同步，AI路径功能已集成，GPT-Load代理服务已部署
- **Nginx架构**: ✨ **新架构已部署** - 模块化配置管理系统正常运行，支持自动化项目管理

### 3. 📂 Shared目录详解（AI必读）

#### 目录作用和状态
- **主要用途**: 备用配置存储、SSL证书备份、向后兼容支持
- **管理状态**: 大部分脚本已弃用，不推荐日常使用
- **重要性**: 了解其内容有助于故障排除和配置理解

#### 关键文件说明
```
shared/nginx/system-nginx.conf  # 备用Nginx配置
├── 包含New-API、Love和GPT-Load的完整路径映射
├── 使用正确的端口配置（New-API:3000, Love:1314, GPT-Load:3001）
├── 当Love项目配置不可用时的备选方案
└── 与生产配置保持同步更新

shared/nginx/ssl/               # SSL证书备份
├── Let's Encrypt证书的备份副本
├── 用于证书恢复和故障排除
└── 包含完整的证书链

shared/nginx/                   # ✨ **新Nginx架构** (重要)
├── conf.d/                     # 项目配置文件目录
│   ├── liangliangdamowang.edu.deal.conf  # 主站完整配置
│   ├── new-api.conf            # New-API项目配置
│   ├── love.conf               # Love项目配置
│   └── gpt-load.conf           # GPT-Load项目配置
├── snippets/                   # 可复用配置片段
│   ├── ssl-params.conf         # SSL/TLS安全配置
│   ├── proxy-params.conf       # 标准代理配置
│   ├── security-headers.conf   # 安全头配置
│   ├── cors-headers.conf       # CORS跨域配置
│   └── cache-static.conf       # 静态文件缓存
├── templates/                  # 配置模板
│   ├── project.tpl             # 通用项目模板
│   └── spa.tpl                 # 单页应用模板
├── scripts/                    # 管理脚本
├── backup/                     # 配置备份
│   └── production-20250725-*/  # 生产环境完整备份
└── README.md                   # 架构说明文档

shared/scripts/                 # 已弃用脚本（了解即可）
├── workspace-manager.sh        # 功能已被各项目独立脚本替代
└── health-monitor.sh          # 监控功能已集成到Love管理脚本
```

#### 网络配置理解要点
1. **配置架构**: ✨ **新模块化架构** - conf.d配置 + snippets复用 + 自动化管理
2. **配置层次**: 生产配置(conf.d) > 备用配置(shared) > 历史配置
3. **端口映射**: New-API(3000) + Love(1314) + GPT-Load(3001) → 统一443端口
4. **SSL管理**: Let's Encrypt自动续期，备份存储在shared/nginx/ssl/
5. **管理工具**: nginx-manager.sh提供完整的配置管理功能
6. **配置复用**: snippets实现SSL、代理、安全头等配置的标准化复用
4. **故障恢复**: 当主配置有问题时，可使用shared中的备用配置

### AI优先使用的工具
1. **一键部署**: `sudo ./deploy.sh` - 全新环境完整部署
2. **New-API管理**: `cd new-api && ./new-api-manager.sh` - New-API项目管理
3. **Love项目管理**: `cd love && ./manage.sh` - Love项目完整管理（包含网络配置）
4. **服务验证**: `cd love && ./manage.sh` 选择"验证所有服务状态"
5. **外部测试**: `cd love && ./manage.sh` 选择"测试外部访问"
6. **配置管理**: `cd love && ./manage.sh` 选择"部署管理" → "配置完整Nginx"

### 新架构特点
- **多项目工作空间**: 包含New-API、Love、Shared三个主要部分
- **独立项目管理**: 每个项目有独立的文档、配置和部署脚本
- **共享资源**: 通过shared目录共享脚本、配置和工具
- **统一仓库**: 所有项目在一个Git仓库中统一管理
- **配置同步**: 通过工具确保配置文件一致性

### 文档结构
- **workspace/guide.md**: AI专用项目指南（本文件）
- **workspace/README.md**: 工作空间总体说明
- **new-api/README.md**: New-API项目详细文档
- **love/README.md**: 情侣网站项目文档
- **shared/README.md**: 共享资源说明

### 项目管理方式
- **New-API**: 使用 `new-api/new-api-manager.sh` 统一管理工具
- **Love**: 使用项目内脚本或shared脚本管理，支持love域名一键配置
- **跨项目**: 使用 `shared/scripts/` 中的共享脚本

### 部署策略
- **独立部署**: 每个项目可以独立部署和运行
- **统一部署**: 使用 `deploy.sh` 进行综合部署
- **分步部署**: 先部署New-API，再部署Love项目
- **Love域名**: 支持love域名的一键式配置和SSL设置

### AI推荐操作顺序
1. **全新部署**: 使用 `sudo ./deploy.sh` 进行完整部署
2. **快速诊断**: 使用 `cd love && ./manage.sh` 选择"验证所有服务状态"
3. **项目管理**:
   - New-API: `cd new-api && ./new-api-manager.sh`
   - Love项目: `cd love && ./manage.sh`
4. **配置管理**: 通过Love管理脚本的"部署管理"进行Nginx配置
5. **外部测试**: 使用Love管理脚本的"测试外部访问"功能
6. **备份管理**: 通过各项目管理脚本进行数据备份
7. **详细文档**: 查看各项目的README.md获取详细说明

### AI故障排除流程
1. **系统状态检查**: `cd love && ./manage.sh` → 选择"验证所有服务状态"
2. **外部访问测试**: `cd love && ./manage.sh` → 选择"测试外部访问"
3. **配置问题修复**: `cd love && ./manage.sh` → 选择"部署管理" → "配置完整Nginx"
4. **New-API问题**: `cd new-api && ./new-api-manager.sh status`
5. **Love服务问题**: `cd love && ./manage.sh` → 选择"服务管理"
6. **数据库问题**: `cd love && ./manage.sh` → 选择"数据库管理"
7. **查看详细日志**: `cd love && ./manage.sh logs` 或 `cd new-api && docker-compose logs`

## 🚀 Deploy-All 使用指南 (重构版)

### 完整部署
```bash
sudo ./deploy.sh              # 完整部署所有项目
```

### 分步部署
```bash
sudo ./deploy.sh --check      # 仅检查环境
sudo ./deploy.sh --new-api    # 仅部署New-API
sudo ./deploy.sh --love       # 仅部署Love项目
sudo ./deploy.sh --network    # 仅配置网络(Nginx/SSL)
sudo ./deploy.sh --verify     # 仅验证部署结果
```

### 数据管理
```bash
# 备份数据
./deploy-all/scripts/backup.sh                    # 完整备份
./deploy-all/scripts/backup.sh --compress         # 压缩备份
./deploy-all/scripts/backup.sh --new-api          # 仅备份New-API

# 恢复数据
./deploy-all/scripts/restore.sh --list            # 列出可用备份
./deploy-all/scripts/restore.sh --from backup_*   # 从备份恢复

# 清理环境
./deploy-all/scripts/cleanup.sh --all             # 完全清理
./deploy-all/scripts/cleanup.sh --services        # 仅停止服务
./deploy-all/scripts/cleanup.sh --dry-run         # 模拟运行
```

### 项目管理
```bash
# New-API管理
cd new-api && ./new-api-manager.sh

# Love项目管理
cd love && ./manage.sh

# 查看服务状态
sudo systemctl status nginx love-site
docker ps | grep new-api
```

### 架构优势
- **模块化**: 每个功能独立模块，易于维护和扩展
- **可复用**: 模块可独立使用或组合使用
- **错误隔离**: 单个模块失败不影响其他模块
- **详细日志**: 每个步骤都有详细的日志输出
- **利用现有**: 调用现有项目脚本，避免重复实现

---

**注意**: 这个指南专为AI助手设计，提供了项目的完整概览和快速理解路径。如需详细的技术文档，请参考各子项目的README.md文件和deploy-all/guide.md。
