version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 挂载整个nginx配置目录到容器内相同路径
      - /root/workspace/shared/nginx:/root/workspace/shared/nginx:rw
      # 同时挂载到标准nginx路径以确保正确加载
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./conf.d:/etc/nginx/conf.d:ro
      - ./includes:/etc/nginx/includes:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./www:/var/www:ro
      # 挂载日志目录
      - ./logs/access:/var/log/nginx/access:rw
      - ./logs/error:/var/log/nginx/error:rw
    networks:
      - nginx-network
      - new-api_default  # 连接到new-api网络
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    environment:
      - TZ=Asia/Shanghai



networks:
  nginx-network:
    driver: bridge
  new-api_default:
    external: true  # 使用外部已存在的new-api网络

volumes:
  nginx_logs:
    driver: local
