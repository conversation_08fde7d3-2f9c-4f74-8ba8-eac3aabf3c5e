# Nginx日志目录

## 目录结构

```
logs/
├── access/               # 访问日志目录
│   ├── liangliangdamowang.edu.deal.log
│   └── liangliangdamowang.edu.deal-http.log
└── error/                # 错误日志目录
    └── liangliangdamowang.edu.deal.log
```

## 日志轮转配置

创建 `/etc/logrotate.d/nginx-custom` 文件：

```
/root/workspace/shared/nginx/logs/access/*.log
/root/workspace/shared/nginx/logs/error/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose -f /root/workspace/shared/nginx/docker-compose.yml exec nginx nginx -s reload
    endscript
}
```

## 日志监控

可以使用以下命令监控日志：

```bash
# 实时查看访问日志
tail -f logs/access/liangliangdamowang.edu.deal.log

# 实时查看错误日志
tail -f logs/error/liangliangdamowang.edu.deal.log

# 分析访问统计
awk '{print $1}' logs/access/liangliangdamowang.edu.deal.log | sort | uniq -c | sort -nr | head -10
```
