# 访问控制配置

# IP白名单（根据需要修改）
# allow ***********/24;    # 内网访问
# allow 10.0.0.0/8;        # 内网访问
# deny all;                # 拒绝其他所有IP

# 禁止访问敏感文件
location ~ /\. {
    deny all;
    access_log off;
    log_not_found off;
}

# 禁止访问备份文件
location ~ ~$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 禁止访问配置文件
location ~* \.(conf|ini|log|bak|sql|tar|gz|zip)$ {
    deny all;
    access_log off;
    log_not_found off;
}

# 防止恶意User-Agent
if ($http_user_agent ~* (nmap|nikto|wikto|sf|sqlmap|bsqlbf|w3af|acunetix|havij|appscan)) {
    return 444;
}

# 防止恶意请求方法
if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$) {
    return 444;
}

# 防止SQL注入和XSS攻击
if ($args ~* (union|select|insert|delete|update|drop|create|alter|exec|script|alert|prompt|confirm)) {
    return 444;
}

# 地理位置限制（可选，需要GeoIP模块）
# if ($geoip_country_code !~ ^(CN|US|JP)$) {
#     return 444;
# }
